<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ModelPenjadwalanOperasi extends CI_Model {

    public function getDataPerjanjian($start, $length, $searchValue, $mulai, $akhir, $hari, $tab)
    {
        $query = "SELECT 
            rmp.ID, 
            ppo.id id_penjadwalan, 
            rr.id id_reservasi, 
            IFNULL(ppo.tgl_operasi, rmp.TANGGAL) tgl_operasi,
            dk.nama kamar_operasi, 
            IFNULL(ppo.created_at, rr.tgl_input) tgl_dibuat, 
            ps.NAMA nama, 
            wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', TIMESTAMPDIFF(YEAR, ps.TANGGAL_LAHIR, CURDATE()), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, 
            wlo.diagnosis, 
            wlo.tin<PERSON>,
            peg_op.NAMA dokter_operator, 
            peg_an.NAMA dokter_anestesi,
            tpo.catatan_khusus, 
            rr.status, 
            ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
            CASE 
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs
        FROM remun_medis.perjanjian rmp
        LEFT JOIN medis.tb_waiting_list_operasi wlo ON wlo.id = rmp.ID_WAITING_LIST_OPERASI
        LEFT JOIN perjanjian.penjadwalan_operasi ppo ON ppo.id_waiting_list_operasi = wlo.id
        LEFT JOIN db_master.tb_kamar dk ON dk.id = ppo.kamar_operasi AND dk.id_ruang IN ('105090101', '105090104')
        LEFT JOIN master.dokter d ON d.ID = wlo.id_dokter
        LEFT JOIN master.pegawai peg_op ON peg_op.NIP = d.NIP
        LEFT JOIN master.dokter da ON da.ID = ppo.dr_anestesi
        LEFT JOIN master.pegawai peg_an ON peg_an.NIP = da.NIP
        LEFT JOIN medis.tb_pendaftaran_operasi tpo ON wlo.id_pendaftaran_operasi = tpo.id
        LEFT JOIN master.pasien ps ON ps.NORM = wlo.norm
        LEFT JOIN db_reservasi.tb_reservasi rr ON rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID
        LEFT JOIN master.referensi ref ON ref.ID = ppo.tujuan_rs AND ref.jenis = 81
        LEFT JOIN master.ruangan mr ON mr.ID = ppo.ruang_rawat
        WHERE rmp.STATUS != 0
            AND rmp.ID_RUANGAN = '105090104'
            AND wlo.id IS NOT NULL
            AND (ppo.status = 1 OR ppo.id IS NULL)";

        // Filter berdasarkan tanggal
        if (!empty($mulai) && !empty($akhir)) {
            $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) BETWEEN '$mulai' AND '$akhir'";
        }

        // Filter berdasarkan hari
        if (!empty($hari) && $hari != 'All') {
            $query .= " AND DAYNAME(IFNULL(ppo.tgl_operasi, rmp.TANGGAL)) = '$hari'";
        }

        // Filter berdasarkan tab
        if (!empty($tab)) {
            switch ($tab) {
                case 'hari_ini':
                    $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) = CURDATE()";
                    break;
                case 'history':
                    $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) < CURDATE() AND ppo.status = 1";
                    break;
                case 'batal':
                    $query .= " AND (ppo.status = 0 OR rmp.STATUS = 0)";
                    break;
                case 'daftar':
                default:
                    // Default filter untuk daftar perjanjian
                    $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >= CURDATE()";
                    break;
            }
        }

        // Search
        if (!empty($searchValue)) {
            $query .= " AND (ps.NAMA LIKE '%$searchValue%' OR wlo.norm LIKE '%$searchValue%' OR wlo.diagnosis LIKE '%$searchValue%' OR wlo.tindakan LIKE '%$searchValue%')";
        }

        // Count total records
        $totalQuery = "SELECT COUNT(*) as total FROM ($query) as count_table";
        $totalResult = $this->db->query($totalQuery)->row();
        $recordsTotal = $totalResult->total;
        $recordsFiltered = $recordsTotal;

        // Order and limit
        $query .= " ORDER BY IFNULL(ppo.tgl_operasi, rmp.TANGGAL) DESC";
        if ($length != -1) {
            $query .= " LIMIT $start, $length";
        }

        $result = $this->db->query($query)->result();

        return array(
            'data' => $result,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    public function getDetailPerjanjian($id)
    {
        $query = "SELECT 
            ppo.id AS id_penjadwalan,
            rmp.id AS id_perjanjian,
            rr.id AS id_reservasi,
            wlo.id AS id_waiting_list_operasi,
            tpo.id AS id_tpo,
            mr.JENIS_KELAMIN AS id_jk,
            mr.NORM AS norm,
            mr.NAMA AS nama,
            kp.NOMOR AS no_telp,
            wlo.id_dokter,
            tpo.diagnosa_medis AS diagnosis,
            tpo.rencana_tindakan_operasi AS tindakan,
            rr.tgl_rencanaMasuk AS tgl_rawat,
            IF(ppo.tgl_operasi IS NULL, rmp.TANGGAL, ppo.tgl_operasi) AS tgl_operasi,
            ppo.kamar_operasi,
            TIME_FORMAT(ppo.waktu_operasi, '%H:%i') AS waktu_operasi,
            ppo.durasi_operasi,
            ppo.dr_anestesi,
            IF(ppo.jenis_anestesi IS NULL, tpo.rencana_jenis_pembiusan, ppo.jenis_anestesi) AS jenis_anestesi,
            tpo.rencana_jenis_pembiusan_lain,
            ppo.menunggu_konfirmasi_ruang,
            ppo.ruang_rawat,
            IF(ppo.tujuan_rs IS NULL, IF(rr.id_cara_bayar = 2, 2, 16), ppo.tujuan_rs) AS tujuan_rs
        FROM medis.tb_waiting_list_operasi wlo
        LEFT JOIN perjanjian.penjadwalan_operasi ppo ON ppo.id_waiting_list_operasi = wlo.id
        LEFT JOIN remun_medis.perjanjian rmp ON rmp.ID_WAITING_LIST_OPERASI = wlo.id
        LEFT JOIN db_reservasi.tb_reservasi rr ON rr.id_perjanjian = rmp.ID
        LEFT JOIN medis.tb_pendaftaran_operasi tpo ON tpo.id = wlo.id_pendaftaran_operasi
        LEFT JOIN master.pasien mr ON mr.NORM = wlo.norm
        LEFT JOIN master.kontak_pasien kp ON kp.NORM = mr.NORM AND kp.JENIS = 3
        WHERE rmp.ID_RUANGAN = '105090104' AND rmp.ID = '$id'";

        return $this->db->query($query)->row_array();
    }

    public function simpanPerjanjian($data)
    {
        $this->db->trans_start();

        try {
            if (!empty($data['id_perjanjian'])) {
                // Update existing
                $this->updatePerjanjian($data);
            } else {
                // Insert new
                $this->insertPerjanjian($data);
            }

            $this->db->trans_complete();
            return $this->db->trans_status();
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return false;
        }
    }

    private function insertPerjanjian($data)
    {
        // Insert perjanjian
        $perjanjian_data = array(
            'ID_WAITING_LIST_OPERASI' => $data['id_waiting_list'],
            'ID_RUANGAN' => '105090104',
            'TANGGAL' => $data['tgl_operasi'],
            'STATUS' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => $this->session->userdata('id_simpel')
        );
        $this->db->insert('remun_medis.perjanjian', $perjanjian_data);
        $id_perjanjian = $this->db->insert_id();

        // Insert penjadwalan operasi
        $jadwal_data = array(
            'id_waiting_list_operasi' => $data['id_waiting_list'],
            'kamar_operasi' => $data['kamar_operasi'],
            'tgl_operasi' => $data['tgl_operasi'],
            'waktu_operasi' => $data['waktu_operasi'],
            'durasi_operasi' => $data['durasi_operasi'],
            'dr_anestesi' => !empty($data['dr_anestesi']) ? $data['dr_anestesi'] : null,
            'jenis_anestesi' => !empty($data['jenis_anestesi']) ? $data['jenis_anestesi'] : null,
            'menunggu_konfirmasi_ruang' => !empty($data['menunggu_konfirmasi_ruang']) ? $data['menunggu_konfirmasi_ruang'] : null,
            'ruang_rawat' => !empty($data['ruang_rawat']) ? $data['ruang_rawat'] : null,
            'tujuan_rs' => $data['tujuan_rs'],
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => $this->session->userdata('id_simpel')
        );
        $this->db->insert('perjanjian.penjadwalan_operasi', $jadwal_data);
    }

    private function updatePerjanjian($data)
    {
        // Update perjanjian
        $perjanjian_data = array(
            'TANGGAL' => $data['tgl_operasi'],
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->userdata('id_simpel')
        );
        $this->db->where('ID', $data['id_perjanjian']);
        $this->db->update('remun_medis.perjanjian', $perjanjian_data);

        // Update penjadwalan operasi
        $jadwal_data = array(
            'kamar_operasi' => $data['kamar_operasi'],
            'tgl_operasi' => $data['tgl_operasi'],
            'waktu_operasi' => $data['waktu_operasi'],
            'durasi_operasi' => $data['durasi_operasi'],
            'dr_anestesi' => !empty($data['dr_anestesi']) ? $data['dr_anestesi'] : null,
            'jenis_anestesi' => !empty($data['jenis_anestesi']) ? $data['jenis_anestesi'] : null,
            'menunggu_konfirmasi_ruang' => !empty($data['menunggu_konfirmasi_ruang']) ? $data['menunggu_konfirmasi_ruang'] : null,
            'ruang_rawat' => !empty($data['ruang_rawat']) ? $data['ruang_rawat'] : null,
            'tujuan_rs' => $data['tujuan_rs'],
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->userdata('id_simpel')
        );
        $this->db->where('id', $data['id_penjadwalan']);
        $this->db->update('perjanjian.penjadwalan_operasi', $jadwal_data);
    }

    // public function hapusPerjanjian($id, $alasan)
    // {
    //     $this->db->trans_start();

    //     // Soft delete penjadwalan operasi
    //     $this->db->where('id_waiting_list_operasi', "(SELECT ID_WAITING_LIST_OPERASI FROM remun_medis.perjanjian WHERE ID = '$id')", false);
    //     $this->db->update('perjanjian.penjadwalan_operasi', array(
    //         'status' => 0,
    //         'alasan_batal' => $alasan,
    //         'cancel_at' => date('Y-m-d H:i:s'),
    //         'cancel_by' => $this->session->userdata('id_simpel')
    //     ));

    //     // Soft delete perjanjian
    //     $this->db->where('ID', $id);
    //     $this->db->update('remun_medis.perjanjian', array(
    //         'STATUS' => 0,
    //         'DELETED_AT' => date('Y-m-d H:i:s'),
    //         'DELETED_BY' => $this->session->userdata('id_simpel')
    //     ));

    //     $this->db->trans_complete();
    //     return $this->db->trans_status();
    // }

    public function getPasien($search = '')
    {
        $query = "SELECT DISTINCT p.NORM as norm, p.NAMA as nama 
                  FROM master.pasien p 
                  INNER JOIN medis.tb_waiting_list_operasi wlo ON wlo.norm = p.NORM 
                  WHERE 1=1";
        
        if (!empty($search)) {
            $query .= " AND (p.NORM LIKE '%$search%' OR p.NAMA LIKE '%$search%')";
        }
        
        $query .= " ORDER BY p.NAMA ASC LIMIT 20";
        
        return $this->db->query($query)->result_array();
    }

    public function getPasienDetail($norm)
    {
        $query = "SELECT p.NAMA as nama, p.JENIS_KELAMIN as jk, p.TANGGAL_LAHIR as tgl_lahir, 
                         kp.NOMOR as telepon
                  FROM master.pasien p
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = p.NORM AND kp.JENIS = 3
                  WHERE p.NORM = '$norm'";
        
        return $this->db->query($query)->row_array();
    }

    public function getDokter()
    {
        $query = "SELECT d.ID as id, p.NAMA as nama 
                  FROM master.dokter d
                  LEFT JOIN master.pegawai p ON p.NIP = d.NIP
                  WHERE d.STATUS = 1 
                  ORDER BY p.NAMA ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getKamarOperasi()
    {
        $query = "SELECT id, nama 
                  FROM db_master.tb_kamar 
                  WHERE id_ruang = '105090104' AND status = 1
                  ORDER BY nama ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getKamarOperasiForCalendar()
    {
        $query = "SELECT id, nama,
                  COALESCE(jam_mulai, '08:00') as jam_mulai,
                  COALESCE(jam_selesai, '16:00') as jam_selesai,
                  COALESCE(lama_op, 60) as lama_op,
                  COALESCE(jeda_op, 60) as jeda_op
                  FROM db_master.tb_kamar 
                  WHERE id_ruang = '105090104' AND status = 1
                  ORDER BY nama ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getDokterAnestesi()
    {
        $query = "SELECT d.ID as id, p.NAMA as nama 
                  FROM master.dokter d
                  LEFT JOIN master.pegawai p ON p.NIP = d.NIP
                  WHERE d.STATUS = 1 
                  AND p.SMF IN (SELECT id_smf FROM db_master.tb_smf_anestesi)
                  ORDER BY p.NAMA ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getJenisAnestesi()
    {
        $query = "SELECT ID as id, DESKRIPSI as nama 
                  FROM master.referensi 
                  WHERE jenis = 80 
                  ORDER BY DESKRIPSI ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getRuangRawat()
    {
        $query = "SELECT ID as id, DESKRIPSI as nama
                  FROM master.ruangan
                  WHERE JENIS_KUNJUNGAN = 3
                  ORDER BY DESKRIPSI ASC";

        return $this->db->query($query)->result_array();
    }

    // Method untuk tab Daftar Perjanjian Operasi (Query 1)
    public function getDaftarPerjanjian($start, $length, $searchValue, $mulai, $akhir, $hari, $orderBy, $orderDir, $status_filter = 'penjadwalan,perjanjian')
    {
        $this->db->select(
            "rmp.ID, 
            (CASE WHEN ppo.status = 0 THEN NULL ELSE ppo.id END) as id_penjadwalan, 
            rr.id id_reservasi, IFNULL(ppo.tgl_operasi, rmp.TANGGAL) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(ppo.created_at, rr.tgl_input) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', TIMESTAMPDIFF(YEAR, ps.TANGGAL_LAHIR, CURDATE()), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            peg_op.NAMA dokter_operator, peg_an.NAMA dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,tpo.id id_tpo,
            IFNULL(ppo.slot_operasi, tpo.slot_operasi) AS slot_operasi,
            IFNULL(ppo.kamar_operasi, tpo.kamar) AS kamar_id,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
             CASE
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        , false);
        $this->db->from('remun_medis.perjanjian rmp');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang = '105090104'", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.pegawai peg_op', 'peg_op.NIP = d.NIP', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('master.pegawai peg_an', 'peg_an.NIP = da.NIP', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->where('rmp.STATUS !=', 0);
        $this->db->where('rmp.ID_RUANGAN', '105090104');
        $this->db->where('wlo.id IS NOT NULL');
        $this->db->where('(ppo.status NOT IN (5) OR ppo.id IS NULL)', null, false);

        // Filter tanggal
        if ($mulai != null) {
            $this->db->where('IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >=', $mulai);
        }
        if ($akhir != null) {
            $this->db->where('IFNULL(ppo.tgl_operasi, rmp.TANGGAL) <=', $akhir);
        }
        // Hapus batasan default <= CURDATE() agar semua data tampil

        // Filter hari
        if ($hari != 'All') {
            $this->db->where("DAYNAME(IFNULL(ppo.tgl_operasi, rmp.TANGGAL)) = '".$this->db->escape_str($hari)."'", null, false);
        }

        // Search
        if (!empty($searchValue)) {
            $this->db->group_start();
            $this->db->like('ps.NAMA', $searchValue);
            $this->db->or_like('wlo.norm', $searchValue);
            $this->db->or_like('wlo.diagnosis', $searchValue);
            $this->db->or_like('wlo.tindakan', $searchValue);
            $this->db->group_end();
        }

        // Status filter
        if (!empty($status_filter) && $status_filter !== 'penjadwalan,perjanjian') {
            $statusArray = explode(',', $status_filter);
            $this->db->group_start();

            if (in_array('penjadwalan', $statusArray) && !in_array('perjanjian', $statusArray)) {
                // Hanya penjadwalan
                $this->db->where('ppo.id IS NOT NULL');
            } elseif (in_array('perjanjian', $statusArray) && !in_array('penjadwalan', $statusArray)) {
                // Hanya perjanjian
                $this->db->where('ppo.id IS NULL');
            }

            $this->db->group_end();
        }

        // Count total records
        $totalQuery = clone $this->db;
        $recordsTotal = $totalQuery->count_all_results();
        $recordsFiltered = $recordsTotal;

        // Filter untuk tidak menampilkan penjadwalan yang sudah kelewat
        // $this->db->where('(ppo.id IS NULL OR IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >= CURRENT_DATE)');

        // Custom ordering: perjanjian baru penjadwalan, tanggal terdekat dulu, tanggal kelewat di bawah
        if ($orderBy && $orderBy != 'no') {
            if ($orderBy == 'tgl_operasi') {
                // Custom sorting berdasarkan prioritas yang diminta
                $this->db->order_by("
                    CASE
                        WHEN ppo.id IS NULL THEN 1  -- Perjanjian prioritas pertama
                        WHEN ppo.id IS NOT NULL THEN 2  -- Penjadwalan prioritas kedua
                    END", "ASC", false);

                // Dalam setiap kategori, urutkan berdasarkan tanggal (terdekat dulu, kelewat di bawah)
                $this->db->order_by("
                    CASE
                        WHEN IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >= CURRENT_DATE THEN IFNULL(ppo.tgl_operasi, rmp.TANGGAL)
                        ELSE DATE_ADD(IFNULL(ppo.tgl_operasi, rmp.TANGGAL), INTERVAL 100 YEAR)
                    END", $orderDir, false);
            } else {
                // Sorting biasa untuk kolom lain, dengan custom priority sebagai secondary
                $this->db->order_by($orderBy, $orderDir);
                $this->db->order_by("
                    CASE
                        WHEN ppo.id IS NULL THEN 1
                        WHEN ppo.id IS NOT NULL THEN 2
                    END", "ASC", false);
                $this->db->order_by("
                    CASE
                        WHEN IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >= CURRENT_DATE THEN IFNULL(ppo.tgl_operasi, rmp.TANGGAL)
                        ELSE DATE_ADD(IFNULL(ppo.tgl_operasi, rmp.TANGGAL), INTERVAL 100 YEAR)
                    END", "ASC", false);
            }
        } else {
            // Default sorting dengan prioritas yang diminta
            $this->db->order_by("
                CASE
                    WHEN ppo.id IS NULL THEN 1  -- Perjanjian dulu
                    WHEN ppo.id IS NOT NULL THEN 2  -- Penjadwalan kemudian
                END", "ASC", false);

            // Dalam setiap kategori, tanggal terdekat dulu, kelewat di bawah
            $this->db->order_by("
                CASE
                    WHEN IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >= CURRENT_DATE THEN IFNULL(ppo.tgl_operasi, rmp.TANGGAL)
                    ELSE DATE_ADD(IFNULL(ppo.tgl_operasi, rmp.TANGGAL), INTERVAL 100 YEAR)
                END", "ASC", false);
        }
        $this->db->limit($length, $start);

        $query = $this->db->get();
        $data = $query->result();

        return array(
            'data' => $data,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    // Method untuk tab Operasi Hari Ini (Query 2)
    public function getOperasiHariIni($start, $length, $searchValue, $orderBy, $orderDir)
    {
        $this->db->select(
            "rmp.ID, ppo.id id_penjadwalan, rr.id id_reservasi, IFNULL(ppo.tgl_operasi, rmp.TANGGAL) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(ppo.created_at, rr.tgl_input) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', TIMESTAMPDIFF(YEAR, ps.TANGGAL_LAHIR, CURDATE()), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            peg_op.NAMA dokter_operator, peg_an.NAMA dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
            mr2.DESKRIPSI ruang_rawat_desc,
             CASE
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        );
        $this->db->from('remun_medis.perjanjian rmp');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang = '105090104'", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.pegawai peg_op', 'peg_op.NIP = d.NIP', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('master.pegawai peg_an', 'peg_an.NIP = da.NIP', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');
        $this->db->join('master.ruangan mr2', 'mr2.ID = rmp.ID_RUANGAN', 'left');
        $this->db->where('rmp.STATUS', 1);
        $this->db->where('rmp.ID_RUANGAN', '105090104');

        $this->db->group_start();
        // ppo.id IS NULL → rr.id_cara_bayar != 2
        $this->db->group_start();
        $this->db->where('ppo.id IS NULL', null, false);
        $this->db->where('rr.id_cara_bayar !=', 2);
        $this->db->group_end();
        // ppo.id IS NOT NULL → ppo.tujuan_rs = 16
        $this->db->or_group_start();
        $this->db->where('ppo.id IS NOT NULL', null, false);
        $this->db->where('ppo.tujuan_rs', 16);
        $this->db->group_end();
        $this->db->group_end();

        $this->db->where('rmp.TANGGAL = CURDATE()');
        $this->db->where('ppo.status !=', 0);
        $this->db->where('ppo.status !=', 5);

        // Search
        if (!empty($searchValue)) {
            $this->db->group_start();
            $this->db->like('ps.NAMA', $searchValue);
            $this->db->or_like('wlo.norm', $searchValue);
            $this->db->or_like('wlo.diagnosis', $searchValue);
            $this->db->or_like('wlo.tindakan', $searchValue);
            $this->db->group_end();
        }

        // Count total records
        $totalQuery = clone $this->db;
        $recordsTotal = $totalQuery->count_all_results();
        $recordsFiltered = $recordsTotal;

        // Order and limit
        if ($orderBy && $orderBy != 'no') {
            $this->db->order_by($orderBy, $orderDir);
        }
        $this->db->limit($length, $start);

        $query = $this->db->get();
        $data = $query->result();

        return array(
            'data' => $data,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    // Method untuk tab History/Selesai Operasi (Query 3)
    public function getHistoryOperasi($start, $length, $searchValue, $mulai, $akhir, $hari, $orderBy, $orderDir)
    {
        $this->db->select(
            "rmp.ID, ppo.id id_penjadwalan, rr.id id_reservasi, IFNULL(rmp.TANGGAL, ppo.tgl_operasi) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(rr.tgl_input, ppo.created_at) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', TIMESTAMPDIFF(YEAR, ps.TANGGAL_LAHIR, CURDATE()), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            peg_op.NAMA dokter_operator, peg_an.NAMA dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
            CASE
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        );
        $this->db->from('remun_medis.perjanjian rmp');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang = '105090104'", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.pegawai peg_op', 'peg_op.NIP = d.NIP', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('master.pegawai peg_an', 'peg_an.NIP = da.NIP', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');

        $this->db->where('rmp.STATUS', 1);
        $this->db->where_in('ppo.status', array(0, 5));
        $this->db->where('rmp.ID_RUANGAN', '105090104');

        // Filter tanggal
        if ($mulai != null) {
            $this->db->where('rmp.TANGGAL >=', $mulai);
        }
        if ($akhir != null) {
            $this->db->where('rmp.TANGGAL <=', $akhir);
        } else {
            $this->db->where('rmp.TANGGAL <= CURDATE()');
        }

        // Filter hari
        if ($hari != 'All') {
            $this->db->where("DAYNAME(ppo.tgl_operasi) = '".$this->db->escape_str($hari)."'", null, false);
        }

        // Search
        if (!empty($searchValue)) {
            $this->db->group_start();
            $this->db->like('ps.NAMA', $searchValue);
            $this->db->or_like('wlo.norm', $searchValue);
            $this->db->or_like('wlo.diagnosis', $searchValue);
            $this->db->or_like('wlo.tindakan', $searchValue);
            $this->db->group_end();
        }

        // Count total records
        $totalQuery = clone $this->db;
        $recordsTotal = $totalQuery->count_all_results();
        $recordsFiltered = $recordsTotal;

        // Order and limit
        if ($orderBy && $orderBy != 'no') {
            $this->db->order_by($orderBy, $orderDir);
        }
        $this->db->limit($length, $start);

        $query = $this->db->get();
        $data = $query->result();

        return array(
            'data' => $data,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    // Method untuk mendapatkan hari yang tersedia dalam rentang tanggal
    public function getAvailableDays($mulai, $akhir)
    {
        $available_days = array();

        if ($mulai && $akhir) {
            $start_date = new DateTime($mulai);
            $end_date = new DateTime($akhir);

            $days_in_range = array();

            while ($start_date <= $end_date) {
                $day_name = $start_date->format('l'); // Monday, Tuesday, etc.
                $days_in_range[] = $day_name;
                $start_date->add(new DateInterval('P1D'));
            }

            $unique_days = array_unique($days_in_range);

            $day_mapping = array(
                'Monday' => 'Monday',
                'Tuesday' => 'Tuesday',
                'Wednesday' => 'Wednesday',
                'Thursday' => 'Thursday',
                'Friday' => 'Friday',
                'Saturday' => 'Saturday',
                'Sunday' => 'Sunday'
            );

            foreach ($day_mapping as $english => $indonesian) {
                $available_days[$english] = in_array($english, $unique_days);
            }
        }

        return $available_days;
    }

    // Method isiForm untuk mengambil data form - Optimized query
    public function isiForm($jenis, $ID)
    {
        // Optimized query dengan limit dan index yang tepat
        if ($jenis == 'buat') {
            // Query untuk form buat baru berdasarkan tpo.id
            $this->db->select([
                "'' AS id_penjadwalan",
                "rmp.id AS id_perjanjian",
                'rr.id AS id_reservasi',
                'wlo.id AS id_waiting_list_operasi',
                'tpo.id AS id_tpo',
                'mr.JENIS_KELAMIN AS id_jk',
                'mr.NORM AS norm',
                'mr.NAMA AS nama',
                'kp.NOMOR AS no_telp',
                'wlo.id_dokter',
                'db_rekammedis.getNamaLengkapDokter(wlo.id_dokter) AS nama_dokter',
                'tpo.diagnosa_medis AS diagnosis',
                'tpo.rencana_tindakan_operasi AS tindakan',
                'rr.tgl_rencanaMasuk AS tgl_rawat',
                'rmp.TANGGAL AS tgl_operasi',
                "tpo.kamar AS kamar_operasi",
                'tpo.jam_operasi AS waktu_operasi',
                'tpo.perkiraan_lama_operasi AS durasi_operasi',
                "'' AS dr_anestesi",
                'tpo.rencana_jenis_pembiusan AS jenis_anestesi',
                'tpo.rencana_jenis_pembiusan_lain',
                "'' AS ruang_rawat",
                'tpo.nokun',
                'tpo.diagnosa_medis',
                'tpo.rencana_jenis_pembiusan',
                'tpo.perkiraan_lama_operasi',
                'rr.id_cara_bayar',
                'rr.id_kelas',
                'cb.DESKRIPSI cara_bayar',
                'tpo.slot_operasi',
                '(
                    SELECT GROUP_CONCAT(
                        CONCAT(TIME_FORMAT(lpo.JAM_MULAI, "%H:%i"), "-", TIME_FORMAT(lpo.JAM_AKHIR, "%H:%i"))
                        ORDER BY lpo.JAM_MULAI SEPARATOR ","
                    )
                    FROM log.log_pendaftaran_operasi lpo
                     WHERE lpo.ID_PERJANJIAN = rmp.ID
                     AND lpo.STATUS IN (1, 2)
                ) AS slot_operation_log'
            ], FALSE);

            $this->db->from('medis.tb_pendaftaran_operasi tpo');
            $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
            $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
            $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID', 'left');
            $this->db->join('master.pasien mr', 'mr.NORM = wlo.norm', 'left');
            $this->db->join('master.kontak_pasien kp', 'kp.NORM = mr.NORM AND kp.JENIS = 3', 'left');
            $this->db->join('master.referensi cb', 'cb.ID = rr.id_cara_bayar AND cb.JENIS = 10', 'left');
            $this->db->where('tpo.id', $ID);

        } elseif ($jenis == 'ubah') {
            // Query untuk form ubah berdasarkan ppo.id
            $this->db->select([
                'ppo.id AS id_penjadwalan',
                'rmp.id AS id_perjanjian',
                'rr.id AS id_reservasi',
                'wlo.id AS id_waiting_list_operasi',
                'tpo.id AS id_tpo',
                'mr.JENIS_KELAMIN AS id_jk',
                'mr.NORM AS norm',
                'mr.NAMA AS nama',
                'kp.NOMOR AS no_telp',
                'wlo.id_dokter',
                'tpo.diagnosa_medis AS diagnosis',
                'tpo.rencana_tindakan_operasi AS tindakan',
                'rr.tgl_rencanaMasuk AS tgl_rawat',
                'COALESCE(ppo.tgl_operasi, rmp.TANGGAL) AS tgl_operasi',
                'COALESCE(ppo.kamar_operasi, tpo.kamar) AS kamar_operasi',
                'COALESCE(TIME_FORMAT(ppo.waktu_operasi, "%H:%i"), tpo.jam_operasi) AS waktu_operasi',
                'COALESCE(ppo.durasi_operasi, tpo.perkiraan_lama_operasi) AS durasi_operasi',
                'ppo.dr_anestesi',
                'COALESCE(ppo.jenis_anestesi, tpo.rencana_jenis_pembiusan) AS jenis_anestesi',
                'tpo.rencana_jenis_pembiusan_lain',
                'ppo.ruang_rawat',
                'tpo.nokun',
                'tpo.diagnosa_medis',
                'tpo.rencana_jenis_pembiusan',
                'tpo.perkiraan_lama_operasi',
                'COALESCE(ppo.tujuan_rs, 16) AS tujuan_rs',
                'rr.id_cara_bayar',
                'rr.id_kelas',
                'cb.DESKRIPSI cara_bayar',
                'COALESCE(ppo.slot_operasi, tpo.slot_operasi) AS slot_operasi',
                '(
                    SELECT GROUP_CONCAT(
                        CONCAT(TIME_FORMAT(lpo.JAM_MULAI, "%H:%i"), "-", TIME_FORMAT(lpo.JAM_AKHIR, "%H:%i"))
                        ORDER BY lpo.JAM_MULAI SEPARATOR ","
                    )
                    FROM log.log_pendaftaran_operasi lpo
                     WHERE lpo.ID_PERJANJIAN = rmp.ID
                     AND lpo.STATUS IN (1, 2)
                ) AS slot_operation_log'
            ]);

            $this->db->from('perjanjian.penjadwalan_operasi ppo');
            $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = ppo.id_waiting_list_operasi', 'left');
            $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
            $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID', 'left');
            $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
            $this->db->join('master.pasien mr', 'mr.NORM = wlo.norm', 'left');
            $this->db->join('master.kontak_pasien kp', 'kp.NORM = mr.NORM AND kp.JENIS = 3', 'left');
            $this->db->join('master.referensi cb', 'cb.ID = rr.id_cara_bayar AND cb.JENIS = 10', 'left');
            $this->db->where('ppo.id', $ID);
        }

        $this->db->limit(1); // Tambahkan limit untuk optimasi
        $query = $this->db->get();
        return $query->row_array();
    }

    // Method dokter
    public function dokter()
    {
        $this->db->select(
            'd.ID id_dokter, p.NAMA dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from('master.dokter d');
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method kamar
    public function kamar($id_ruang)
    {
        $this->db->select('id, nama, status');
        $this->db->from('db_master.tb_kamar');
        $this->db->where('id_ruang', $id_ruang);
        $this->db->where('status', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method dokter_anestesi
    public function dokter_anestesi()
    {
        $this->db->select(
            'd.ID id_dokter, p.NAMA dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from('master.dokter d');
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where_in('smf.ID', [6, 46, 55]);
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method referensi
    public function referensi($id)
    {
        $this->db->select('id_variabel, variabel, nilai, status, status_checked');
        $this->db->from('db_master.variabel');
        $this->db->where('id_referensi', $id);
        $this->db->order_by('id_variabel', 'asc');
        $this->db->order_by('seq', 'asc');
        $this->db->where('status !=', 0);
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method to fetch tindakan details for surgeons
    public function ambilTindakan($id)
    {
        // Get main surgeon tindakan
        $this->db->select('
            db_rekammedis.getNamaLengkapDokter(tpo.dokter_bedah) AS nama_dokter,
            tpo.rencana_tindakan_operasi,
            dv.variabel as tindakan
        ');
        $this->db->from('medis.tb_pendaftaran_operasi tpo');
        $this->db->join('db_master.variabel dv', 'dv.id_variabel = tpo.tindakan_operasi', 'left');
        $this->db->where('tpo.id', $id);
        $this->db->where('dv.id_referensi', 1868);
        $this->db->where('dv.status', 1);
        $mainSurgeonQuery = $this->db->get();
        $mainSurgeonData = $mainSurgeonQuery->row_array();
        
        // Get assistant surgeons tindakan
        $this->db->select('
            db_rekammedis.getNamaLengkapDokter(tapo.asisten_bedah) AS nama_dokter_lain,
            tapo.rencana_tindakan,
            dv.variabel as tindakan
        ');
        $this->db->from('medis.tb_asisten_pendaftaran_operasi tapo');
        $this->db->join('db_master.variabel dv', 'dv.id_variabel = tapo.tindakan_asisten_bedah', 'left');
        $this->db->where('tapo.status', 1);
        $this->db->where('tapo.id_pendaftaran', $id);
        $this->db->where('dv.id_referensi', 1868);
        $this->db->where('dv.status', 1);
        $this->db->order_by('tapo.id', 'asc');
        $assistantSurgeonsQuery = $this->db->get();
        $assistantSurgeonsData = $assistantSurgeonsQuery->result_array();
        
        // Get log tindakan
        $this->db->select('
            lpo.RENCANA_TINDAKAN as rencana_tindakan_log,
            dv.variabel as tindakan
        ');
        $this->db->from('log.log_pendaftaran_operasi_tindakan lpo');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID = lpo.ID_PERJANJIAN', 'left');
        $this->db->join('db_master.variabel dv', 'dv.id_variabel = lpo.TINDAKAN', 'left');
        $this->db->where('rmp.ID_WAITING_LIST_OPERASI = (SELECT wlo.id FROM medis.tb_waiting_list_operasi wlo WHERE wlo.id_pendaftaran_operasi = '.$id.')');
        $this->db->where('lpo.status !=', 0);
        $this->db->where('dv.id_referensi', 1868);
        $this->db->where('dv.status', 1);
        $logQuery = $this->db->get();
        $logData = $logQuery->result_array();
        
        return array(
            'main_surgeon' => $mainSurgeonData,
            'assistant_surgeons' => $assistantSurgeonsData,
            'log_tindakan' => $logData
        );
    }

    // public function deletePenjadwalan($id, $alasan)
    // {
    //     $this->db->trans_start();

    //     try {
    //         // Ambil data terkait berdasarkan id penjadwalan
    //         $this->db->select('ppo.id id_ppo, rmp.ID id_perjanjian, rr.id id_reservasi, tpo.id id_tpo, wlo.id id_wlo');
    //         $this->db->from('perjanjian.penjadwalan_operasi ppo');
    //         $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = ppo.id_waiting_list_operasi', 'left');
    //         $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
    //         $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
    //         $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID AND rr.status != 0', 'left');
    //         $this->db->where('ppo.id', $id);
    //         $query = $this->db->get();
    //         $data = $query->row();

    //         if (!$data) {
    //             throw new Exception('Data penjadwalan operasi tidak ditemukan');
    //         }

    //         $user_id = $this->session->userdata('id_simpel');
    //         $timestamp = date('Y-m-d H:i:s');

    //         // 1. Update perjanjian.penjadwalan_operasi (soft delete)
    //         $this->db->where('id', $id);
    //         $this->db->update('perjanjian.penjadwalan_operasi', array(
    //             'status' => 0,
    //             'alasan_batal' => $alasan,
    //             'cancel_at' => $timestamp,
    //             'cancel_by' => $user_id
    //         ));

    //         // 2. Update tabel terkait lainnya juga (opsional, tergantung kebutuhan bisnis)
    //         // Contoh: mengembalikan status perjanjian, wlo, dll.
    //         // Untuk saat ini, kita hanya membatalkan jadwalnya saja sesuai contoh.
    //         // Jika relasi lain juga harus dibatalkan, logikanya bisa ditambahkan di sini.

    //         $this->db->trans_complete();
    //         return $this->db->trans_status();

    //     } catch (Exception $e) {
    //         $this->db->trans_rollback();
    //         // Sebaiknya log error di sini
    //         // log_message('error', 'Gagal hapus penjadwalan: ' . $e->getMessage());
    //         return false;
    //     }
    // }

    // Method getCalendarData 
    public function getCalendarData($start_date, $end_date)
    {
        // Query untuk mendapatkan data penjadwalan
        $query = "SELECT
            c.status AS status_jadwal,
            c.id AS id_penjadwalan,
            rmp.ID AS perjanjian,
            c.tgl_operasi AS tanggal_operasi,
            c.waktu_operasi,
            c.slot_operasi,
            c.kamar_operasi,
            k.nama AS nama_kamar,
            CONCAT(wlo.norm, ' - ', master.getNamaLengkap(wlo.norm)) AS pasien_info,
            db_rekammedis.getNamaLengkapDokter(wlo.id_dokter) AS nama_dokter,
            tpo.rencana_tindakan_operasi,
            CONCAT(
                TIME_FORMAT(c.waktu_operasi, '%H:%i'),
                ' - ',
                DATE_FORMAT(
                    ADDTIME(
                        STR_TO_DATE(c.waktu_operasi, '%H:%i'),
                        SEC_TO_TIME(c.durasi_operasi * 60)
                    ),
                    '%H:%i'
                )
            ) AS waktu_selesai
        FROM perjanjian.penjadwalan_operasi c
        LEFT JOIN remun_medis.perjanjian rmp ON rmp.ID = c.id_perjanjian
        LEFT JOIN medis.tb_waiting_list_operasi wlo ON wlo.id = rmp.ID_WAITING_LIST_OPERASI
        LEFT JOIN medis.tb_pendaftaran_operasi tpo ON tpo.id = wlo.id_pendaftaran_operasi
        #LEFT JOIN master.pasien ps ON ps.NORM = wlo.norm
        #LEFT JOIN master.dokter d ON d.ID = wlo.id_dokter
        #LEFT JOIN master.pegawai peg ON peg.NIP = d.NIP
        LEFT JOIN db_master.tb_kamar k ON k.id = c.kamar_operasi AND k.id_ruang = '105090104'
        WHERE
            k.id_ruang = '105090104' AND
            c.tgl_operasi BETWEEN ? AND ?
            AND c.STATUS != 0
        ORDER BY c.tgl_operasi ASC, c.waktu_operasi ASC";

        $result = $this->db->query($query, array($start_date, $end_date))->result_array();

        // Query untuk mendapatkan daftar kamar
        $kamar_query = "SELECT id, nama FROM db_master.tb_kamar WHERE id_ruang = '105090104' AND status = 1 ORDER BY nama ASC";
        $kamar_result = $this->db->query($kamar_query)->result_array();

        // Organize data by date and slot with priority (scheduled > appointment)
        $calendar_data = array();

        // Add kamar list to each date
        foreach ($result as $row) {
            $date = $row['tanggal_operasi'];

            if (!isset($calendar_data[$date])) {
                $calendar_data[$date] = array();
                $calendar_data[$date]['kamar_list'] = $kamar_result;
            }

            // Create slot key: slot_kamarId_slotNumber
            $slot_key = 'slot_' . $row['kamar_operasi'] . '_' . $row['slot_operasi'];
            
            // Priority logic: scheduled overrides appointment
            if (!isset($calendar_data[$date][$slot_key])) {
                // Slot is empty, fill it
                $calendar_data[$date][$slot_key] = $row;
            } else {
                // Slot already has data, check priority
                $existing = $calendar_data[$date][$slot_key];
                if ($existing['jenis'] == 'scheduled') {
                    // Keep existing scheduled, skip this row
                    continue;
                } elseif ($existing['jenis'] == 'appointment' && $row['jenis'] == 'scheduled') {
                    // Replace appointment with scheduled
                    $calendar_data[$date][$slot_key] = $row;
                }
                // If both are appointments or other cases, keep the first one
            }
        }

        return $calendar_data;
    }

    // Method savePenjadwalan
    // public function savePenjadwalan($data)
    // {
    //     if (isset($data['id_penjadwalan']) && !empty($data['id_penjadwalan'])) {
    //         // Update existing record
    //         $this->db->where('id', $data['id_penjadwalan']);
    //         unset($data['id_penjadwalan']); // Remove ID from data array
    //         return $this->db->update('perjanjian.penjadwalan_operasi', $data);
    //     } else {
    //         // Insert new record
    //         unset($data['id_penjadwalan']); // Remove empty ID
    //         $data['created_at'] = date('Y-m-d H:i:s');
    //         return $this->db->insert('perjanjian.penjadwalan_operasi', $data);
    //     }
    // }
    //    Kelas tower C aja
    function getKelas(){
        if ($this->input->get('q')) {
            $this->db->like('mr.DESKRIPSI ', $this->input->get('q'));
        }
        $this->db->select('mr.ID, mr.DESKRIPSI');
        $this->db->from('master.ruang_kamar rk');
        $this->db->join('master.referensi mr', 'rk.KELAS = mr.ID AND mr.JENIS = 19');
        $this->db->where('rk.STATUS !=', 0);
        $this->db->where('mr.STATUS !=', 0);
        $this->db->where_in('mr.ID', [64, 2, 62, 63, 61]);
        $this->db->group_by('rk.KELAS');
        $this->db->order_by('FIELD(mr.ID, 64, 2, 62, 63, 61)');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    // Method updateReservasi
    public function updateReservasi($id_reservasi, $data)
    {
        $this->db->where('id', $id_reservasi);
        return $this->db->update('db_reservasi.tb_reservasi', $data);
    }

    public function hapusPenjadwalanOperasi($id, $alasan)
    {
        $this->db->trans_start();

        try {
            // Ambil data terkait berdasarkan id perjanjian
            $this->db->select('ppo.id id_ppo, rmp.ID id_perjanjian, rr.id id_reservasi, tpo.id id_tpo, wlo.id id_wlo');
            $this->db->from('perjanjian.penjadwalan_operasi ppo');
            $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = ppo.id_waiting_list_operasi', 'left');
            $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
            $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
            $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID AND rr.status != 0', 'left');
            $this->db->where('ppo.id', $id);
            $query = $this->db->get();
            $data = $query->row();

            if (!$data) {
                throw new Exception('Data perjanjian tidak ditemukan');
            }

            $user_id = $this->session->userdata('id_simpel');
            $timestamp = date('Y-m-d H:i:s');

            // Update status = 0 untuk semua tabel terkait

            // 1. Update perjanjian.penjadwalan_operasi jika ada
            if ($data->id_ppo) {
                $this->db->where('id', $data->id_ppo);
                $this->db->update('perjanjian.penjadwalan_operasi', array(
                    'status' => 0,
                    'alasan_batal' => $alasan,
                    'cancel_at' => $timestamp,
                    'cancel_by' => $user_id
                ));
            }

            // 2. Update medis.tb_pendaftaran_operasi
            if ($data->id_tpo) {
                $this->db->where('id', $data->id_tpo);
                $this->db->update('medis.tb_pendaftaran_operasi', array(
                    'status' => 0
                ));
            }

            // 3. Update medis.tb_waiting_list_operasi
            if ($data->id_wlo) {
                $this->db->where('id', $data->id_wlo);
                $this->db->update('medis.tb_waiting_list_operasi', array(
                    'status' => 0
                ));
            }

            // 4. Update db_reservasi.tb_reservasi jika ada
            if ($data->id_reservasi) {
                $this->db->where('id', $data->id_reservasi);
                $this->db->update('db_reservasi.tb_reservasi', array(
                    'status' => 0,
                    'alasan_edit' => $alasan,
                ));
            }

            // 5. Update remun_medis.perjanjian (STATUS = 0)
            $this->db->where('ID', $id);
            $this->db->update('remun_medis.perjanjian', array(
                'STATUS' => 0,
                'DELETED_AT' => date('Y-m-d H:i:s'),
                'DELETED_BY' => $this->session->userdata('id_simpel')
            ));

            $this->db->trans_complete();
            return $this->db->trans_status();

        } catch (Exception $e) {
            $this->db->trans_rollback();
            return false;
        }
    }


    // Method hapusPerjanjianOperasi - untuk hapus data perjanjian
    public function hapusPerjanjianOperasi($id_perjanjian)
    {
        $this->db->trans_start();

        try {
            // Ambil data terkait berdasarkan id perjanjian
            $this->db->select('rmp.ID, rr.id id_reservasi, tpo.id id_tpo, wlo.id id_wlo, ppo.id id_ppo');
            $this->db->from('remun_medis.perjanjian rmp');
            $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
            $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
            $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID AND rr.status != 0', 'left');
            $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id AND ppo.status != 0', 'left');
            $this->db->where('rmp.ID', $id_perjanjian);
            $query = $this->db->get();
            $data = $query->row();

            if (!$data) {
                throw new Exception('Data perjanjian tidak ditemukan');
            }

            $user_id = $this->session->userdata('id_simpel');
            $timestamp = date('Y-m-d H:i:s');

            // Update status = 0 untuk semua tabel terkait

            // 1. Update perjanjian.penjadwalan_operasi jika ada
            // if ($data->id_ppo) {
            //     $this->db->where('id', $data->id_ppo);
            //     $this->db->update('perjanjian.penjadwalan_operasi', array(
            //         'status' => 0,
            //         'alasan_batal' => $alasan,
            //         'cancel_at' => $timestamp,
            //         'cancel_by' => $user_id
            //     ));
            // }

            // 2. Update medis.tb_pendaftaran_operasi
            if ($data->id_tpo) {
                $this->db->where('id', $data->id_tpo);
                $this->db->update('medis.tb_pendaftaran_operasi', array(
                    'status' => 0
                ));
            }

            // 3. Update medis.tb_waiting_list_operasi
            if ($data->id_wlo) {
                $this->db->where('id', $data->id_wlo);
                $this->db->update('medis.tb_waiting_list_operasi', array(
                    'status' => 0
                ));
            }

            // 4. Update db_reservasi.tb_reservasi jika ada
            if ($data->id_reservasi) {
                $this->db->where('id', $data->id_reservasi);
                $this->db->update('db_reservasi.tb_reservasi', array(
                    'status' => 0
                ));
            }

            // 5. Update remun_medis.perjanjian (STATUS = 0)
            $this->db->where('ID', $id_perjanjian);
            $this->db->update('remun_medis.perjanjian', array(
                'STATUS' => 0,
                'DELETED_AT' => date('Y-m-d H:i:s'),
                'DELETED_BY' => $this->session->userdata('id_simpel')
            ));

            $this->db->trans_complete();
            return $this->db->trans_status();

        } catch (Exception $e) {
            $this->db->trans_rollback();
            return false;
        }
    }

    // Method selesaiOperasi - Update status penjadwalan = 5
    public function selesaiOperasi($id_penjadwalan)
    {
        $user_id = $this->session->userdata('id_simpel');
        $timestamp = date('Y-m-d H:i:s');

        $this->db->where('id', $id_penjadwalan);
        return $this->db->update('perjanjian.penjadwalan_operasi', array(
            'status' => 5,
            'selesai_operasi' => $timestamp
        ));
    }
    
    public function hapusOperasiHariIni($id_penjadwalan, $id_perjanjian, $alasan)
    {
        $this->db->trans_start();

        try {
            // Ambil data terkait berdasarkan id penjadwalan
            $this->db->select('ppo.id id_ppo, rmp.ID, rr.id id_reservasi, tpo.id id_tpo, wlo.id id_wlo');
            $this->db->from('perjanjian.penjadwalan_operasi ppo');
            $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = ppo.id_waiting_list_operasi', 'left');
            $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
            $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
            $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID AND rr.status != 0', 'left');
            $this->db->where('ppo.id', $id_penjadwalan);
            $query = $this->db->get();
            $data = $query->row();

            if (!$data) {
                throw new Exception('Data operasi tidak ditemukan');
            }

            $user_id = $this->session->userdata('id_simpel');
            $timestamp = date('Y-m-d H:i:s');

            // Update status = 0 untuk semua tabel terkait

            // 1. Update perjanjian.penjadwalan_operasi
            $this->db->where('id', $id_penjadwalan);
            $this->db->update('perjanjian.penjadwalan_operasi', array(
                'status' => 0,
                'alasan_batal' => $alasan,
                'cancel_at' => $timestamp,
                'cancel_by' => $user_id
            ));

            // 2. Update medis.tb_pendaftaran_operasi
            if ($data->id_tpo) {
                $this->db->where('id', $data->id_tpo);
                $this->db->update('medis.tb_pendaftaran_operasi', array(
                    'status' => 0,
                    // 'cancel_by' => $user_id
                ));
            }

            // 3. Update medis.tb_waiting_list_operasi
            if ($data->id_wlo) {
                $this->db->where('id', $data->id_wlo);
                $this->db->update('medis.tb_waiting_list_operasi', array(
                    'status' => 0,
                    // 'alasan_batal' => $alasan,
                    // 'cancel_at' => $timestamp,
                    // 'cancel_by' => $user_id
                ));
            }

            // 4. Update remun_medis.perjanjian
            if ($data->ID) {
                $this->db->where('ID', $data->ID);
                $this->db->update('remun_medis.perjanjian', array(
                    'STATUS' => 0,
                    'DELETED_AT' => date('Y-m-d H:i:s'),
                    'DELETED_BY' => $this->session->userdata('id_simpel')
                ));
            }

            // 5. Update db_reservasi.tb_reservasi jika ada
            if ($data->id_reservasi) {
                $this->db->where('id', $data->id_reservasi);
                $this->db->update('db_reservasi.tb_reservasi', array(
                    'status' => 0,
                    'alasan_edit' => $alasan,
                    // 'cancel_at' => $timestamp,
                    // 'cancel_by' => $user_id
                ));
            }

            $this->db->trans_complete();
            return $this->db->trans_status();

        } catch (Exception $e) {
            $this->db->trans_rollback();
            return false;
        }
    }

    // Method getWeekCalendarData - Enhanced to read from log.log_pendaftaran_operasi directly
    public function getWeekCalendarData($start_date, $end_date)
    {
        // Query to get data directly from log.log_pendaftaran_operasi per day & room
        $query = "SELECT
            lpo.TANGGAL as tanggal_operasi,
            lpo.ID_TB_KAMAR as kamar_operasi,
            k.nama AS nama_kamar,
            COALESCE(k.jam_mulai, '08:00') as jam_mulai_kamar,
            COALESCE(k.jam_selesai, '16:00') as jam_selesai_kamar,
            k.lama_op,
            k.jeda_op,
            TIME_FORMAT(lpo.JAM_MULAI, '%H:%i') as JAM_MULAI,
            TIME_FORMAT(lpo.JAM_AKHIR, '%H:%i') as JAM_AKHIR,
            lpo.STATUS,
            lpo.ID_PERJANJIAN,
            CONCAT(wlo.norm, ' - ', master.getNamaLengkap(wlo.norm)) AS pasien_info,
            db_rekammedis.getNamaLengkapDokter(wlo.id_dokter) AS nama_dokter,
            tpo.rencana_tindakan_operasi,
            CASE 
                WHEN lpo.STATUS = 2 THEN 'scheduled'
                WHEN lpo.STATUS = 1 THEN 'appointment'
                ELSE 'scheduled'
            END AS jenis,
            lpo.STATUS AS status_jadwal,
            rmp.STATUS AS status_perjanjian
        FROM log.log_pendaftaran_operasi lpo
        LEFT JOIN db_master.tb_kamar k ON k.id = lpo.ID_TB_KAMAR AND k.id_ruang = '105090104'
        LEFT JOIN remun_medis.perjanjian rmp ON rmp.ID = lpo.ID_PERJANJIAN
        LEFT JOIN medis.tb_waiting_list_operasi wlo ON wlo.id = rmp.ID_WAITING_LIST_OPERASI
        LEFT JOIN medis.tb_pendaftaran_operasi tpo ON tpo.id = wlo.id_pendaftaran_operasi
        WHERE
            lpo.TANGGAL BETWEEN ? AND ?
            AND lpo.STATUS IN (1, 2)
            AND k.id_ruang = '105090104'
        ORDER BY lpo.TANGGAL ASC, lpo.JAM_MULAI ASC";

        $result = $this->db->query($query, array($start_date, $end_date))->result_array();

        // Organize data by date and calculate proper slot numbers
        $calendar_data = array();

        foreach ($result as $row) {
            $date = $row['tanggal_operasi'];
            $kamar_id = $row['kamar_operasi'];
            
            if (!isset($calendar_data[$date])) {
                $calendar_data[$date] = array();
            }

            // Calculate proper slot numbers based on time and room operational hours
            $jamMulai = $row['jam_mulai_kamar'];
            $lamaOp = intval($row['lama_op'] ?? 60);
            
            // Convert times to minutes since start of day
            $startMinutes = $this->timeToMinutes($jamMulai);
            $slotStartMinutes = $this->timeToMinutes($row['JAM_MULAI']);
            $slotEndMinutes = $this->timeToMinutes($row['JAM_AKHIR']);
            
            // Calculate slot numbers (1-based)
            $slotStart = floor(($slotStartMinutes - $startMinutes) / $lamaOp) + 1;
            $slotEnd = floor(($slotEndMinutes - $startMinutes) / $lamaOp);
            
            // Ensure slot numbers are valid
            $slotStart = max(1, $slotStart);
            $slotEnd = max($slotStart, $slotEnd);
            
            // Expand slot range for multi-slot operations
            for ($slotNum = $slotStart; $slotNum <= $slotEnd; $slotNum++) {
                $slot_key = 'slot_' . $kamar_id . '_' . $slotNum;
                
                if (!isset($calendar_data[$date][$slot_key])) {
                    // Add expanded slot data
                    $slotData = $row;
                    $slotData['slot'] = $slotNum;
                    $slotData['slot_start'] = $slotStart;
                    $slotData['slot_end'] = $slotEnd;
                    $slotData['waktu_selesai'] = $row['JAM_MULAI'] . ' - ' . $row['JAM_AKHIR'];
                    $calendar_data[$date][$slot_key] = $slotData;
                } else {
                    // Handle conflict - prioritize scheduled over appointment
                    $existing = $calendar_data[$date][$slot_key];
                    if ($existing['jenis'] === 'appointment' && $row['jenis'] === 'scheduled') {
                        $slotData = $row;
                        $slotData['slot'] = $slotNum;
                        $slotData['slot_start'] = $slotStart;
                        $slotData['slot_end'] = $slotEnd;
                        $slotData['waktu_selesai'] = $row['JAM_MULAI'] . ' - ' . $row['JAM_AKHIR'];
                        $calendar_data[$date][$slot_key] = $slotData;
                    }
                }
            }
        }

        return $calendar_data;
    }
    
    // Helper method to convert time string to minutes
    private function timeToMinutes($timeString) {
        $parts = explode(':', $timeString);
        return intval($parts[0]) * 60 + intval($parts[1]);
    }
    
    // Method to update log.log_pendaftaran_operasi based on slot selection
    public function updateLogPendaftaranOperasi($idPerjanjian, $kamarOperasi, $slotOperasiMulti, $tanggalOperasi) {
        $this->db->trans_start();
        
        try {
            // Validate input parameters
            if (empty($idPerjanjian) || empty($kamarOperasi) || empty($tanggalOperasi)) {
                throw new Exception('Required parameters are missing');
            }
            
            // Parse slot_operasi_multi (format: "16:00-17:00,17:00-18:00")
            $timeSlots = array();
            if (!empty($slotOperasiMulti)) {
                $slots = explode(',', $slotOperasiMulti);
                foreach ($slots as $slot) {
                    $slot = trim($slot);
                    if (strpos($slot, '-') !== false) {
                        list($jamMulai, $jamAkhir) = explode('-', $slot);
                        $jamMulai = trim($jamMulai);
                        $jamAkhir = trim($jamAkhir);
                        
                        // Validate time format (HH:MM)
                        if (preg_match('/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/', $jamMulai) && 
                            preg_match('/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/', $jamAkhir)) {
                            $timeSlots[] = array(
                                'jam_mulai' => $jamMulai,
                                'jam_akhir' => $jamAkhir
                            );
                        }
                    }
                }
            }
            
            // If no valid time slots, just update status for different kamar
            if (empty($timeSlots)) {
                $this->db->where('ID_PERJANJIAN', $idPerjanjian);
                $this->db->where('ID_TB_KAMAR !=', $kamarOperasi);
                $this->db->update('log.log_pendaftaran_operasi', array(
                    'STATUS' => 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ));
                
                $this->db->trans_complete();
                return $this->db->trans_status();
            }
            
            // Get existing records for this perjanjian
            $this->db->where('ID_PERJANJIAN', $idPerjanjian);
            $existingRecords = $this->db->get('log.log_pendaftaran_operasi')->result_array();
            
            // Update status=0 for records with different kamar
            if (!empty($existingRecords)) {
                $this->db->where('ID_PERJANJIAN', $idPerjanjian);
                $this->db->where('ID_TB_KAMAR !=', $kamarOperasi);
                $this->db->update('log.log_pendaftaran_operasi', array(
                    'STATUS' => 0,
                    // 'updated_at' => date('Y-m-d H:i:s')
                ));
            }
            
            // Process each time slot
            foreach ($timeSlots as $timeSlot) {
                $jamMulai = $timeSlot['jam_mulai'];
                $jamAkhir = $timeSlot['jam_akhir'];
                
                // Check if record exists with same time range and kamar
                $this->db->where('ID_PERJANJIAN', $idPerjanjian);
                $this->db->where('ID_TB_KAMAR', $kamarOperasi);
                $this->db->where('TIME_FORMAT(JAM_MULAI, "%H:%i") =', $jamMulai);
                $this->db->where('TIME_FORMAT(JAM_AKHIR, "%H:%i") =', $jamAkhir);
                $existing = $this->db->get('log.log_pendaftaran_operasi')->row();
                
                if ($existing) {
                    // Update existing record to status=2
                    $this->db->where('ID', $existing->ID);
                    $this->db->update('log.log_pendaftaran_operasi', array(
                        'STATUS' => 2,
                        'TANGGAL' => $tanggalOperasi,
                        // 'updated_at' => date('Y-m-d H:i:s')
                    ));
                } else {
                    // Insert new record
                    $insertData = array(
                        'ID_PERJANJIAN' => $idPerjanjian,
                        'ID_TB_KAMAR' => $kamarOperasi,
                        'TANGGAL' => $tanggalOperasi,
                        'JAM_MULAI' => $jamMulai . ':00',
                        'JAM_AKHIR' => $jamAkhir . ':00',
                        'STATUS' => 2,
                        // 'created_at' => date('Y-m-d H:i:s')
                    );
                    $this->db->insert('log.log_pendaftaran_operasi', $insertData);
                }
            }
            
            // Update status=0 for records that don't match current slots
            if (!empty($timeSlots)) {
                // Get all current records for this perjanjian and kamar
                $this->db->select('ID, TIME_FORMAT(JAM_MULAI, "%H:%i") as jam_mulai_formatted, TIME_FORMAT(JAM_AKHIR, "%H:%i") as jam_akhir_formatted');
                $this->db->where('ID_PERJANJIAN', $idPerjanjian);
                $this->db->where('ID_TB_KAMAR', $kamarOperasi);
                $this->db->where('STATUS !=', 0);
                $currentRecords = $this->db->get('log.log_pendaftaran_operasi')->result_array();
                
                foreach ($currentRecords as $record) {
                    $isMatch = false;
                    foreach ($timeSlots as $timeSlot) {
                        if ($record['jam_mulai_formatted'] === $timeSlot['jam_mulai'] && 
                            $record['jam_akhir_formatted'] === $timeSlot['jam_akhir']) {
                            $isMatch = true;
                            break;
                        }
                    }
                    
                    // If record doesn't match any current slot, set status=0
                    if (!$isMatch) {
                        $this->db->where('ID', $record['ID']);
                        $this->db->update('log.log_pendaftaran_operasi', array(
                            'STATUS' => 0,
                            // 'updated_at' => date('Y-m-d H:i:s')
                        ));
                    }
                }
            }
            
            $this->db->trans_complete();
            return $this->db->trans_status();
            
        } catch (Exception $e) {
            $this->db->trans_rollback();
            log_message('error', 'Error updating log_pendaftaran_operasi: ' . $e->getMessage());
            return false;
        }
    }

    public function tambahPenjadwalan($data)
    {
        return $this->db->insert('perjanjian.penjadwalan_operasi', $data);
    }

    public function updatePenjadwalan($data)
    {
        $id_penjadwalan = $data['id_penjadwalan'];
        unset($data['id_penjadwalan']);
        
        $this->db->where('id', $id_penjadwalan);
        return $this->db->update('perjanjian.penjadwalan_operasi', $data);
    }

    public function getKamarOperasiNama($kamarId) {
        $this->db->select('nama');
        $this->db->from('db_master.tb_kamar');
        $this->db->where('id', $kamarId);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            return $query->row()->nama;
        }
        
        return null;
    }

    public function saveWhatsAppLog($data) {
        return $this->db->insert('log.log_pendaftaran_operasi_wa', $data);
    }
    
    /**
     * Get time conflicts for a specific appointment
     * 
     * @param string $tanggal Operation date
     * @param int $kamarId Room ID
     * @param int $currentPerjanjianId Current appointment ID
     * @return array Array of conflicting appointments
     */
    public function getTimeConflicts($tanggal, $kamarId, $currentPerjanjianId) {
        if (empty($tanggal) || empty($kamarId)) {
            log_message('debug', 'Empty parameters: tanggal=' . $tanggal . ', kamarId=' . $kamarId);
            return array();
        }
        
        log_message('debug', 'getTimeConflicts called with: tanggal=' . $tanggal . ', kamarId=' . $kamarId . ', currentPerjanjianId=' . $currentPerjanjianId);
        
        // Get time slots for current perjanjian
        $this->db->select('JAM_MULAI, JAM_AKHIR, STATUS');
        $this->db->from('log.log_pendaftaran_operasi');
        $this->db->where('ID_PERJANJIAN', $currentPerjanjianId);
        $this->db->where('TANGGAL', $tanggal);
        $this->db->where('ID_TB_KAMAR', $kamarId);
        $this->db->where('STATUS IN (1, 2)', null, false);
        $currentSlots = $this->db->get()->result_array();
        
        log_message('debug', 'Current slots found: ' . count($currentSlots) . ' for ID=' . $currentPerjanjianId);
        log_message('debug', 'Current slots data: ' . json_encode($currentSlots));
        
        if (empty($currentSlots)) {
            log_message('debug', 'No current slots found for ID=' . $currentPerjanjianId);
            return array();
        }
        
        $conflicts = array();
        
        foreach ($currentSlots as $currentSlot) {
            // Find overlapping appointments for the same date, room, and time
            $this->db->select('ID_PERJANJIAN, JAM_MULAI, JAM_AKHIR, STATUS, (CASE WHEN STATUS = 2 THEN "scheduled" WHEN STATUS = 1 THEN "appointment" ELSE "unknown" END) as conflict_type', false);
            $this->db->from('log.log_pendaftaran_operasi');
            $this->db->where('ID_PERJANJIAN !=', $currentPerjanjianId);
            $this->db->where('TANGGAL', $tanggal);
            $this->db->where('ID_TB_KAMAR', $kamarId);
            $this->db->where('STATUS IN (1, 2)', null, false);
            
            // Check for time overlap - FIXED LOGIC
            $this->db->group_start();
            // Case 1: Current slot starts during existing slot (JAM_MULAI < existing JAM_AKHIR AND JAM_MULAI >= existing JAM_MULAI)
            $this->db->group_start();
            $this->db->where('JAM_MULAI <', $currentSlot['JAM_AKHIR']);
            $this->db->where('JAM_AKHIR >', $currentSlot['JAM_MULAI']);
            $this->db->group_end();
            $this->db->group_end();
            
            $overlappingSlots = $this->db->get()->result_array();
            
            log_message('debug', 'Overlapping slots found: ' . count($overlappingSlots) . ' for current slot: ' . json_encode($currentSlot));
            log_message('debug', 'Overlapping slots data: ' . json_encode($overlappingSlots));
            
            foreach ($overlappingSlots as $overlapping) {
                // Apply conflict logic based on status values
                $shouldAddConflict = false;
                $currentStatusValue = $currentSlot['STATUS'];
                $overlappingStatusValue = $overlapping['STATUS'];
                
                log_message('debug', 'Conflict check: current_status=' . $currentStatusValue . ', overlapping_status=' . $overlappingStatusValue . ', current_id=' . $currentPerjanjianId . ', overlapping_id=' . $overlapping['ID_PERJANJIAN']);
                
                // Both are appointment (STATUS=1) -> both get conflict
                if ($currentStatusValue == 1 && $overlappingStatusValue == 1) {
                    $shouldAddConflict = true;
                    log_message('debug', 'Conflict detected: Both STATUS=1');
                }
                // Current is appointment (STATUS=1) and overlapping is scheduled (STATUS=2) -> current gets conflict
                elseif ($currentStatusValue == 1 && $overlappingStatusValue == 2) {
                    $shouldAddConflict = true;
                    log_message('debug', 'Conflict detected: Current=1, Overlapping=2');
                }
                // Current is scheduled (STATUS=2) and overlapping is appointment (STATUS=1) -> no conflict for current
                // This will be handled when processing the other appointment
                else {
                    log_message('debug', 'No conflict: Current=' . $currentStatusValue . ', Overlapping=' . $overlappingStatusValue);
                }
                
                if ($shouldAddConflict) {
                    $conflicts[] = array(
                        'conflicting_perjanjian_id' => $overlapping['ID_PERJANJIAN'],
                        'conflict_type' => $overlapping['conflict_type'],
                        'jam_mulai' => $overlapping['JAM_MULAI'],
                        'jam_akhir' => $overlapping['JAM_AKHIR'],
                        'status' => $overlapping['STATUS']
                    );
                }
            }
        }
        
        log_message('debug', 'Total conflicts returned: ' . count($conflicts));
        return $conflicts;
    }
    
}

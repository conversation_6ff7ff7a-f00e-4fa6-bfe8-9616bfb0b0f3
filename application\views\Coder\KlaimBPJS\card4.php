<div class="card mb-3 shadow-sm">
    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-clipboard-list"></i> Sistem Grouping - Belum Final & Ana<PERSON><PERSON> by Dr. - Belum / Gagal Terbaca</h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-warning btn-sm">
                <i class="fas fa-eye"></i> View Billing
            </button>
            <button type="button" class="btn btn-primary btn-sm">
                Tipe INACBG
            </button>
            <button type="button" class="btn btn-success btn-sm">
                Ver 5 - non spesialis
            </button>
        </div>
    </div>
    <div class="card-body" style="background-color: #f8f9fa;">
        <!-- Hidden inputs untuk data -->
        <input type="hidden" id="sep_value" value="<?php echo isset($sep) ? $sep : ''; ?>">
        <input type="hidden" id="nopen_value" value="<?php echo isset($nopen) ? $nopen : ''; ?>">

        <!-- Action Buttons Row -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group-vertical w-100" role="group">
                    <button type="button" class="btn btn-primary mb-1">
                        <i class="fas fa-layer-group"></i> Grouping SIMULASI
                    </button>
                    <button type="button" class="btn btn-info mb-1">
                        <i class="fas fa-cogs"></i> Proses Grouping
                    </button>
                    <button type="button" class="btn btn-danger mb-1">
                        <i class="fas fa-times"></i> Final Grouping
                    </button>
                    <button type="button" class="btn btn-warning mb-1">
                        <i class="fas fa-download"></i> Kirim ke Data Center
                    </button>
                    <button type="button" class="btn btn-secondary mb-1">
                        <i class="fas fa-ban"></i> Hapus Grouping
                    </button>
                    <button type="button" class="btn btn-success">
                        <i class="fas fa-check"></i> Cetak Kwitansi
                    </button>
                </div>
            </div>
        </div>

        <!-- Form Section -->
        <form id="groupingForm" class="needs-validation" novalidate>
            <div class="row">
                <!-- Left Column -->
                <div class="col-lg-6">
                    <!-- DPJP Section -->
                    <div class="form-group mb-3">
                        <label for="dpjp" class="form-label font-weight-bold">DPJP:</label>
                        <input type="text" class="form-control form-control-sm" id="dpjp"
                               placeholder="1977030220141210002 - Dr. dr. BAYU BRAHMANA, SpB Subsp Onk" readonly>
                    </div>

                    <!-- Operator/Waktu Section -->
                    <div class="form-group mb-3">
                        <label for="operator" class="form-label font-weight-bold">Operator/Waktu:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm" id="operator"
                                   placeholder="[Operator]" readonly>
                            <input type="text" class="form-control form-control-sm" id="waktu"
                                   placeholder="[Waktu]" readonly>
                        </div>
                    </div>

                    <!-- Tipe Pasien -->
                    <div class="form-group mb-3">
                        <label for="tipe_pasien" class="form-label font-weight-bold">Tipe Pasien:</label>
                        <input type="text" class="form-control form-control-sm" id="tipe_pasien"
                               placeholder="Rawat Jalan" readonly>
                    </div>

                    <!-- Kode INACBG -->
                    <div class="form-group mb-3">
                        <label for="kode_inacbg" class="form-label font-weight-bold">Kode INACBG:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm bg-info text-white"
                                   id="kode_inacbg" placeholder="[Kode INACBG]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text bg-info text-white font-weight-bold">
                                    [Tarif INACBG]
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Deskripsi -->
                    <div class="form-group mb-3">
                        <label for="deskripsi" class="form-label font-weight-bold">Deskripsi:</label>
                        <textarea class="form-control form-control-sm bg-info text-white"
                                  id="deskripsi" rows="2" placeholder="[Deskripsi]" readonly></textarea>
                    </div>

                    <!-- Tarif INACBG -->
                    <div class="form-group mb-3">
                        <label for="tarif_inacbg" class="form-label font-weight-bold">Tarif INACBG:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm"
                                   id="tarif_inacbg" placeholder="[Tarif INACBG]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">[Tarif INACBG]</span>
                            </div>
                        </div>
                    </div>

                    <!-- Special Drug -->
                    <div class="form-group mb-3">
                        <label for="special_drug" class="form-label font-weight-bold">Special Drug:</label>
                        <select class="form-control form-control-sm" id="special_drug">
                            <option value="">[Pilih Special Drug]</option>
                        </select>
                    </div>

                    <!-- ADL Chronic -->
                    <div class="form-group mb-3">
                        <label for="adl_chronic" class="form-label font-weight-bold">ADL Chronic ++ 4Thn:</label>
                        <select class="form-control form-control-sm" id="adl_chronic">
                            <option value="">[Pilih ADL Chronic]</option>
                        </select>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-lg-6">
                    <!-- Kode Sub Acute -->
                    <div class="form-group mb-3">
                        <label for="kode_sub_acute" class="form-label font-weight-bold">Kode Sub Acute:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm"
                                   id="kode_sub_acute" placeholder="[Kode]" readonly>
                            <input type="text" class="form-control form-control-sm"
                                   id="deskripsi_sub_acute" placeholder="[Deskripsi Sub Acute]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">[Tarif Sub Acute]</span>
                            </div>
                        </div>
                    </div>

                    <!-- Kode Chronic -->
                    <div class="form-group mb-3">
                        <label for="kode_chronic" class="form-label font-weight-bold">Kode Chronic:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm"
                                   id="kode_chronic" placeholder="[Kode]" readonly>
                            <input type="text" class="form-control form-control-sm"
                                   id="deskripsi_chronic" placeholder="[Deskripsi Chronic]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">[Tarif Chronic]</span>
                            </div>
                        </div>
                    </div>

                    <!-- Special Procedure -->
                    <div class="form-group mb-3">
                        <label for="special_procedure" class="form-label font-weight-bold">Special Procedure:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm"
                                   id="special_procedure" placeholder="[Kode]" readonly>
                            <input type="text" class="form-control form-control-sm"
                                   id="deskripsi_special_procedure" placeholder="[Deskripsi Special Procedure]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">[Tarif Special Procedure]</span>
                            </div>
                        </div>
                    </div>

                    <!-- Special Prosthetic -->
                    <div class="form-group mb-3">
                        <label for="special_prosthetic" class="form-label font-weight-bold">Special Prosthetic:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm"
                                   id="special_prosthetic" placeholder="[Kode]" readonly>
                            <input type="text" class="form-control form-control-sm"
                                   id="deskripsi_special_prosthetic" placeholder="[Deskripsi Special Prosthetic]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">[Tarif Special Prosthetic]</span>
                            </div>
                        </div>
                    </div>

                    <!-- Special Investigation -->
                    <div class="form-group mb-3">
                        <label for="special_investigation" class="form-label font-weight-bold">Special Investigation:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm"
                                   id="special_investigation" placeholder="[Kode]" readonly>
                            <input type="text" class="form-control form-control-sm"
                                   id="deskripsi_special_investigation" placeholder="[Deskripsi Special Investigation]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">[Tarif Special Investigation]</span>
                            </div>
                        </div>
                    </div>

                    <!-- Special Drug -->
                    <div class="form-group mb-3">
                        <label for="special_drug_right" class="form-label font-weight-bold">Special Drug:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm"
                                   id="special_drug_right" placeholder="[Kode]" readonly>
                            <input type="text" class="form-control form-control-sm"
                                   id="deskripsi_special_drug" placeholder="[Deskripsi Special Drug]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">[Tarif Special Drug]</span>
                            </div>
                        </div>
                    </div>

                    <!-- Total Tarif -->
                    <div class="form-group mb-3">
                        <label for="total_tarif" class="form-label font-weight-bold">Total Tarif:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm bg-warning"
                                   id="total_tarif" placeholder="[Total Tarif]" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text bg-warning font-weight-bold">
                                    [Total Tarif]
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Section - MDC and DRG -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-body p-2">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-2">
                                        <label for="mdc" class="form-label font-weight-bold small">MDC:</label>
                                        <input type="text" class="form-control form-control-sm bg-info text-white"
                                               id="mdc" placeholder="[MDC]" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-2">
                                        <label for="drg" class="form-label font-weight-bold small">DRG:</label>
                                        <input type="text" class="form-control form-control-sm bg-info text-white"
                                               id="drg" placeholder="[DRG]" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
$(document).ready(function(){
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Form validation
    $('#groupingForm').on('submit', function(e) {
        e.preventDefault();
        // Add form submission logic here
    });

    // Button click handlers
    $('.btn-group-vertical .btn').on('click', function() {
        var buttonText = $(this).text().trim();
        console.log('Button clicked: ' + buttonText);
        // Add specific button logic here
    });

    // Auto-resize textareas
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>

<style>
/* Custom styles for this card */
.card-header .btn-group .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.form-label {
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.form-control-sm {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.btn-group-vertical .btn {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem !important;
    margin-bottom: 0.25rem;
}

.input-group-text {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.bg-info.text-white::placeholder {
    color: rgba(255, 255, 255, 0.8) !important;
}

.bg-warning::placeholder {
    color: rgba(0, 0, 0, 0.7) !important;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 0.25rem;
        border-radius: 0.25rem !important;
    }

    .input-group {
        flex-direction: column;
    }

    .input-group .form-control,
    .input-group-append {
        width: 100%;
        margin-bottom: 0.25rem;
    }
}
</style>
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>jadwalanOperasi extends CI_Controller {

    private $nomorAdmin;

    public function __construct()
    {
        parent::__construct();
        if($this->session->userdata('logged_in') == FALSE ){
            redirect('login');  
        }
        
        // Check specific permission
        if($this->session->userdata('admision') != '1' || $this->session->userdata('stat_admision') != '2') {
            show_404();
        }
        
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('ModelPenjadwalanOperasi', 'ModelAdmision'));
        $this->load->library('whatsapp');
        $this->nomorAdmin = $this->ModelAdmision->getAdminPhone(); // Get admin phone number
    }

    private function checkTimeConflict($field) {
        // Debug logging
        log_message('debug', 'Checking conflict for: ID=' . $field->ID . ', Date=' . $field->tgl_operasi . ', Room=' . (isset($field->kamar_id) ? $field->kamar_id : 'N/A'));
        
        // Check if required fields are available
        if (empty($field->tgl_operasi) || empty($field->kamar_id) || empty($field->ID)) {
            log_message('debug', 'Missing required fields for conflict check: tgl_operasi=' . ($field->tgl_operasi ?? 'NULL') . ', kamar_id=' . ($field->kamar_id ?? 'NULL') . ', ID=' . ($field->ID ?? 'NULL'));
            return false;
        }
        
        // Get conflict data from log.log_pendaftaran_operasi
        $conflicts = $this->ModelPenjadwalanOperasi->getTimeConflicts(
            $field->tgl_operasi, 
            $field->kamar_id, 
            $field->ID
        );
        
        // Debug logging
        log_message('debug', 'Conflicts found: ' . count($conflicts) . ' for ID=' . $field->ID);
        if (!empty($conflicts)) {
            log_message('debug', 'Conflict details: ' . json_encode($conflicts));
        }
        
        return !empty($conflicts);
    }

    private function formatIndonesianTgl($date) {
        if (!$date) return '';    
        $timestamp = strtotime($date);
        $months = array(
            1 => 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        );
        
        $day = date('d', $timestamp);
        $month = $months[date('n', $timestamp)];
        $year = date('Y', $timestamp);    
        return $day . ' ' . $month . ' ' . $year;
    }

    private function sendWhatsAppNotification($data, $penjadwalanId, $isUpdate = false) {
        // Only send if phone number is available
        if (empty($data['nohp'])) {
            log_message('info', 'No phone number provided for WhatsApp notification');
            return;
        }

        try {
            // Format phone number
            $nomorTujuan = $data['nohp'];
            if (substr($nomorTujuan, 0, 1) == '0') {
                $nomorTujuan = '+62' . substr($nomorTujuan, 1);
            } elseif (substr($nomorTujuan, 0, 1) != '+') {
                $nomorTujuan = '+62' . $nomorTujuan;
            }

            // Get operation room name
            $kamarNama = '';
            if (!empty($data['kamar_operasi'])) {
                $this->db->select('nama');
                $this->db->from('db_master.tb_kamar');
                $this->db->where('id', $data['kamar_operasi']);
                $kamarQuery = $this->db->get();
                if ($kamarQuery->num_rows() > 0) {
                    $kamarNama = $kamarQuery->row()->nama;
                }
            }

            // Format operation date and time
            $tglOperasi = $this->formatIndonesianTgl($data['tgl_operasi'] ?? null);
            $jamOperasi = $data['waktu_operasi'] ?? '';

            // Prepare message according to the template
            $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : 
                    (date("H") >= '10' && date("H") < "15" ? "Siang" : 
                    (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam"))) 
                    . " Bapak/Ibu";
            
            // Different message content based on whether it's an update or new scheduling
            if ($isUpdate) {
                $isiPasien = ", kami informasikan bahwa operasi Anda telah dijadwalkan ulang pada tanggal " . $tglOperasi . 
                            ", pukul " . $jamOperasi . ", di Kamar Operasi " . $kamarNama;
            } else {
                $isiPasien = ", kami informasikan bahwa operasi Anda telah dijadwalkan pada tanggal " . $tglOperasi . 
                            ", pukul " . $jamOperasi . ", di Kamar Operasi " . $kamarNama;
            }
            
            $tutup = "Mohon mempersiapkan diri sesuai instruksi dokter, membawa dokumen identitas serta kartu jaminan/asuransi, " .
                    "hasil pemeriksaan laboratorium/penunjang yang terbaru, dan perlengkapan pribadi seperlunya. " .
                    "Untuk informasi lebih lanjut, silakan hubungi petugas kami di " . $this->nomorAdmin;

            $messageParams = [
                "1" => $buka,
                "2" => $isiPasien,
                "3" => $tutup
            ];

            // Send WhatsApp message
            $whatsappResponse = $this->whatsapp->kirim_terima_perjanjin($nomorTujuan, $messageParams);
            
            // Save log to database
            $this->saveWhatsAppLog($data['id_perjanjian'] ?? null, $nomorTujuan, $whatsappResponse);
            
        } catch (Exception $e) {
            log_message('error', 'WhatsApp notification failed: ' . $e->getMessage());
        }
    }

    private function saveWhatsAppLog($idPerjanjian, $nohp, $response) {
        try {
            // Parse response to get the actual response
            $responseData = $response;
            if (is_string($response)) {
                $responseData = json_decode($response, true);
            }
            
            // Prepare log data
            $logData = [
                'ID_PERJANJIAN' => $idPerjanjian,
                'NOHP' => $nohp,
                'RESPONSE' => is_array($responseData) ? json_encode($responseData) : $response,
                'CREATED_AT' => date('Y-m-d H:i:s')
            ];
            
            // Insert to log table
            $this->db->insert('log.log_pendaftaran_operasi_wa', $logData);
            
            log_message('info', 'WhatsApp log saved for perjanjian: ' . $idPerjanjian);
            
        } catch (Exception $e) {
            log_message('error', 'Failed to save WhatsApp log: ' . $e->getMessage());
        }
    }

    public function debug_conflict() {
        // Test conflict detection with your specific data
        $testPerjanjianId = 2586449; // STATUS=1 from your screenshot
        $testDate = '2025-09-03';
        $testKamarId = 9;
        
        echo "<h3>Debug Conflict Detection</h3>";
        echo "<p>Testing with ID_PERJANJIAN: $testPerjanjianId, Date: $testDate, Room: $testKamarId</p>";
        
        // Get conflicts
        $conflicts = $this->ModelPenjadwalanOperasi->getTimeConflicts($testDate, $testKamarId, $testPerjanjianId);
        
        echo "<h4>Conflicts Found: " . count($conflicts) . "</h4>";
        if (!empty($conflicts)) {
            echo "<pre>" . json_encode($conflicts, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<p>No conflicts detected</p>";
        }
        
        // Also test the reverse (the other appointment)
        $testPerjanjianId2 = 2618879; // STATUS=2 from your screenshot
        echo "<hr><h4>Testing reverse (ID_PERJANJIAN: $testPerjanjianId2)</h4>";
        $conflicts2 = $this->ModelPenjadwalanOperasi->getTimeConflicts($testDate, $testKamarId, $testPerjanjianId2);
        echo "<p>Conflicts Found: " . count($conflicts2) . "</p>";
        if (!empty($conflicts2)) {
            echo "<pre>" . json_encode($conflicts2, JSON_PRETTY_PRINT) . "</pre>";
        }
        
        // Show raw data from log table
        echo "<hr><h4>Raw Data from log.log_pendaftaran_operasi</h4>";
        $this->db->select('*');
        $this->db->from('log.log_pendaftaran_operasi');
        $this->db->where('TANGGAL', $testDate);
        $this->db->where('ID_TB_KAMAR', $testKamarId);
        $this->db->where('STATUS IN (1, 2)', null, false);
        $rawData = $this->db->get()->result_array();
        echo "<p>Raw records found: " . count($rawData) . "</p>";
        echo "<pre>" . json_encode($rawData, JSON_PRETTY_PRINT) . "</pre>";
        
        // Test the conflict detection logic directly
        echo "<hr><h4>Direct Conflict Logic Test</h4>";
        echo "<p>Testing if appointment ID $testPerjanjianId (STATUS=1) should conflict with appointment ID $testPerjanjianId2 (STATUS=2)</p>";
        
        // Get the time slots for both appointments
        $this->db->select('ID_PERJANJIAN, JAM_MULAI, JAM_AKHIR, STATUS');
        $this->db->from('log.log_pendaftaran_operasi');
        $this->db->where('TANGGAL', $testDate);
        $this->db->where('ID_TB_KAMAR', $testKamarId);
        $this->db->where('ID_PERJANJIAN IN (' . $testPerjanjianId . ', ' . $testPerjanjianId2 . ')', null, false);
        $this->db->where('STATUS IN (1, 2)', null, false);
        $appointmentData = $this->db->get()->result_array();
        
        echo "<p>Appointment data:</p>";
        echo "<pre>" . json_encode($appointmentData, JSON_PRETTY_PRINT) . "</pre>";
        
        // Check if they overlap
        if (count($appointmentData) == 2) {
            $apt1 = $appointmentData[0];
            $apt2 = $appointmentData[1];
            
            // Check time overlap
            $overlap = ($apt1['JAM_MULAI'] < $apt2['JAM_AKHIR'] && $apt1['JAM_AKHIR'] > $apt2['JAM_MULAI']);
            echo "<p>Time overlap detected: " . ($overlap ? "YES" : "NO") . "</p>";
            
            if ($overlap) {
                echo "<p>Conflict rules:</p>";
                echo "<ul>";
                if ($apt1['STATUS'] == 1 && $apt2['STATUS'] == 1) {
                    echo "<li>Both STATUS=1: Both should show conflict</li>";
                } elseif (($apt1['STATUS'] == 1 && $apt2['STATUS'] == 2) || ($apt1['STATUS'] == 2 && $apt2['STATUS'] == 1)) {
                    $conflictAppointment = ($apt1['STATUS'] == 1) ? $apt1['ID_PERJANJIAN'] : $apt2['ID_PERJANJIAN'];
                    echo "<li>One STATUS=1, one STATUS=2: Only appointment ID $conflictAppointment should show conflict</li>";
                }
                echo "</ul>";
            }
        }
    }

    public function index(){
        $data = array(
            'isi' => 'PenjadwalanOperasi/Index'
        );
        $this->load->view('Layout/Wrapper', $data);
    }

    public function get_data()
    {
        $draw = intval($this->input->post("draw"));
        $start = $this->input->post('start');  
        $length = $this->input->post('length'); 
        $searchValue = $this->input->post('search')['value'];
        $mulai = $this->input->post('mulai');
        $akhir = $this->input->post('akhir');
        $hari = $this->input->post('hari');
        $tujuan_rs = $this->input->post('tujuan_rs');
        
        $tab = $this->input->post('tab');
        $listdata = $this->ModelPenjadwalanOperasi->getDataPerjanjian($start, $length, $searchValue, $mulai, $akhir, $hari, $tab);
        
        $data = array();
        $no = $start + 1;
        foreach ($listdata['data'] as $row) {
            $sub_array = array();
            $sub_array[] = $no++;
            $tgl_operasi_formatted = '-';
            if (!empty($row->tgl_operasi) && $row->tgl_operasi !== '0000-00-00') {
                $tgl_operasi_formatted = date('d/m/Y', strtotime($row->tgl_operasi));
            }
            $sub_array[] = $tgl_operasi_formatted;
            $sub_array[] = $row->kamar_operasi ?: '-';
            $sub_array[] = $row->tujuan_rs;
            $tgl_dibuat_formatted = '-';
            if (!empty($row->tgl_dibuat) && $row->tgl_dibuat !== '0000-00-00 00:00:00' && $row->tgl_dibuat !== '0000-00-00') {
                $tgl_dibuat_formatted = date('d/m/Y H:i', strtotime($row->tgl_dibuat));
            }
            $sub_array[] = $tgl_dibuat_formatted;
            $sub_array[] = $row->nama . '<br><small class="text-muted">' . $row->norm . '</small>';
            $sub_array[] = $row->norm;
            $sub_array[] = $row->tgl_lahir_umur;
            $sub_array[] = $row->ruang_rawat ?: '-';
            $sub_array[] = $row->diagnosis ?: '-';
            $sub_array[] = $row->tindakan ?: '-';
            $sub_array[] = $row->dokter_operator ?: '-';
            $sub_array[] = $row->dokter_anestesi ?: '-';
            $sub_array[] = $row->catatan_khusus ?: '-';
            
            // Tombol aksi
            $aksi = '';
            if ($row->id_penjadwalan) {
                $aksi .= '<button type="button" class="btn btn-sm btn-primary mr-1" onclick="editPerjanjian(' . $row->ID . ')" title="Edit">
                            <i class="fe-edit"></i>
                          </button>';
                $aksi .= '<button type="button" class="btn btn-sm btn-danger" onclick="hapusPerjanjian(' . $row->ID . ')" title="Hapus">
                            <i class="fe-trash-2"></i>
                          </button>';
            } else {
                $aksi .= '<button type="button" class="btn btn-sm btn-success" onclick="buatJadwal(' . $row->ID . ')" title="Buat Jadwal">
                            <i class="fe-plus"></i> Jadwal
                          </button>';
            }
            
            $sub_array[] = $aksi;
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $listdata['recordsTotal'],
            "recordsFiltered" => $listdata['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    public function get_detail($id)
    {
        $data = $this->ModelPenjadwalanOperasi->getDetailPerjanjian($id);
        echo json_encode($data);
    }

    public function simpan()
    {
        $this->form_validation->set_rules($this->_validation_rules());
        
        if ($this->form_validation->run() == FALSE) {
            $response = array(
                'status' => 'error',
                'message' => validation_errors()
            );
        } else {
            $data = $this->input->post();
            
            if ($this->ModelPenjadwalanOperasi->simpanPerjanjian($data)) {
                $response = array(
                    'status' => 'success',
                    'message' => 'Data berhasil disimpan'
                );
            } else {
                $response = array(
                    'status' => 'error',
                    'message' => 'Gagal menyimpan data'
                );
            }
        }
        
        echo json_encode($response);
    }

    // public function hapus()
    // {
    //     $id = $this->input->post('id');
    //     $alasan = $this->input->post('alasan');
        
    //     if (empty($alasan)) {
    //         $response = array(
    //             'status' => 'error',
    //             'message' => 'Alasan penghapusan wajib diisi'
    //         );
    //     } else {
    //         if ($this->ModelPenjadwalanOperasi->hapusPerjanjian($id, $alasan)) {
    //             $response = array(
    //                 'status' => 'success',
    //                 'message' => 'Data berhasil dihapus'
    //             );
    //         } else {
    //             $response = array(
    //                 'status' => 'error',
    //                 'message' => 'Gagal menghapus data'
    //             );
    //         }
    //     }
        
    //     echo json_encode($response);
    // }

    public function get_pasien()
    {
        $search = $this->input->get('q');
        $data = $this->ModelPenjadwalanOperasi->getPasien($search);
        
        $results = array();
        foreach ($data as $row) {
            $results[] = array(
                'id' => $row['norm'],
                'text' => $row['norm'] . ' - ' . $row['nama']
            );
        }
        
        echo json_encode(array('results' => $results));
    }

    public function get_pasien_detail($norm)
    {
        $data = $this->ModelPenjadwalanOperasi->getPasienDetail($norm);
        echo json_encode($data);
    }

    public function get_dokter()
    {
        $data = $this->ModelPenjadwalanOperasi->getDokter();
        echo json_encode($data);
    }

    public function get_kamar()
    {
        $data = $this->ModelPenjadwalanOperasi->getKamarOperasi();
        echo json_encode($data);
    }

    public function get_kamar_for_calendar()
    {
        $data = $this->ModelPenjadwalanOperasi->getKamarOperasiForCalendar();
        echo json_encode($data);
    }

    public function get_dokter_anestesi()
    {
        $data = $this->ModelPenjadwalanOperasi->getDokterAnestesi();
        echo json_encode($data);
    }

    public function get_jenis_anestesi()
    {
        $data = $this->ModelPenjadwalanOperasi->getJenisAnestesi();
        echo json_encode($data);
    }

    public function get_ruang_rawat()
    {
        $data = $this->ModelPenjadwalanOperasi->getRuangRawat();
        echo json_encode($data);
    }

    // Data untuk tab Daftar Perjanjian Operasi
    public function get_daftar_perjanjian()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        $hari = $this->input->post('hari') ?: 'All';
        $status_filter = $this->input->post('status_filter') ?: 'penjadwalan,perjanjian';

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'tgl_operasi', 'kamar_operasi', 'tgl_dibuat', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'catatan_khusus', 'tujuan_rs'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_operasi';

        $result = $this->ModelPenjadwalanOperasi->getDaftarPerjanjian($start, $length, $searchValue, $tanggal_mulai, $tanggal_akhir, $hari, $orderBy, $orderDir, $status_filter);

        $data = array();
        $no = $start + 1;

        // Build table data with conflict information
        foreach ($result['data'] as $field) {
            $sub_array = array();
            $sub_array[] = $no;

            // Check konflik slot dari log.log_pendaftaran_operasi
            $hasConflict = $this->checkTimeConflict($field);

            // Format tanggal dengan tanda merah jika ada konflik slot
            $tgl_operasi_formatted = '-';
            if (!empty($field->tgl_operasi) && $field->tgl_operasi !== '0000-00-00') {
                $tgl_operasi_formatted = date('d/m/Y', strtotime($field->tgl_operasi));
            }
            
            if ($hasConflict && $tgl_operasi_formatted !== '-') {
                $sub_array[] = '<span style="color:#dc3545;font-weight:600;background-color:#fff5f5;padding:2px 6px;border-radius:4px;border:1px solid #dc3545;">' . $tgl_operasi_formatted . ' <i class="mdi mdi-alert-circle" title="ruang rawat sudah terisi"></i></span>';
            } else {
                $sub_array[] = $tgl_operasi_formatted;
            }
            
            $sub_array[] = $field->kamar_operasi ?: '-';
            $tgl_dibuat_formatted = '-';
            if (!empty($field->tgl_dibuat) && $field->tgl_dibuat !== '0000-00-00 00:00:00' && $field->tgl_dibuat !== '0000-00-00') {
                $tgl_dibuat_formatted = date('d/m/Y H:i', strtotime($field->tgl_dibuat));
            }
            $sub_array[] = $tgl_dibuat_formatted;
            $sub_array[] = $field->nama;
            $sub_array[] = $field->norm;
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $field->catatan_khusus ?: '-';
            $sub_array[] = $field->tujuan_rs ?: '-';

            // Kolom Aksi - Logic berdasarkan kondisi
            $aksi = '<div class="btn-group" role="group">';
            if (!empty($field->id_penjadwalan)) {
                // Jika ada id_penjadwalan = UBAH
                $aksi .= '<button type="button" class="btn btn-sm btn-primary btn-edit" data-id="'.$field->id_penjadwalan.'" data-jenis="ubah" title="Ubah">';
                $aksi .= '<i class="fa fa-edit"></i>';
                $aksi .= '</button>';
                $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-delete" data-id="'.$field->id_penjadwalan.'" title="Hapus">';
                $aksi .= '<i class="fa fa-times"></i>';
                $aksi .= '</button>';
            } else {
                // Jika tidak ada id_penjadwalan = BUAT dan HAPUS PERJANJIAN
                $aksi .= '<button type="button" class="btn btn-sm btn-success btn-edit" data-id="'.$field->id_tpo.'" data-jenis="buat" title="Buat">';
                $aksi .= '<i class="fa fa-plus"></i>';
                $aksi .= '</button>';
                $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-delete-perjanjian" data-id="'.$field->ID.'" title="Hapus Perjanjian">';
                $aksi .= '<i class="fa fa-trash"></i>';
                $aksi .= '</button>';
            }
            $aksi .= '</div>';
            $sub_array[] = $aksi;

            // Add row attributes for background color
            if (!empty($field->id_penjadwalan)) {
                $sub_array['DT_RowAttr'] = array('style' => 'background-color: #ff0000ff !important; color: #ffffff !important;'); // Hijau
            } else {
                $sub_array['DT_RowAttr'] = array('style' => 'background-color: #c6ee14ff !important;'); // Kuning
            }

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab Operasi Hari Ini
    public function get_operasi_hari_ini()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'kamar_operasi', 'tgl_dibuat', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'catatan_khusus', 'ruang_rawat_desc', 'tujuan_rs'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'kamar_operasi';

        $result = $this->ModelPenjadwalanOperasi->getOperasiHariIni($start, $length, $searchValue, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $field->kamar_operasi ?: '-';
            $tgl_dibuat_formatted = '-';
            if (!empty($field->tgl_dibuat) && $field->tgl_dibuat !== '0000-00-00 00:00:00' && $field->tgl_dibuat !== '0000-00-00') {
                $tgl_dibuat_formatted = date('d/m/Y H:i', strtotime($field->tgl_dibuat));
            }
            $sub_array[] = $tgl_dibuat_formatted;
            $sub_array[] = $field->nama;
            $sub_array[] = $field->norm;
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $field->catatan_khusus ?: '-';
            $sub_array[] = $field->ruang_rawat_desc ?: '-';
            $sub_array[] = $field->tujuan_rs ?: '-';

            // Kolom Aksi untuk Operasi Hari Ini - Selesai dan Hapus
            $aksi = '<div class="btn-group" role="group">';
            if (!empty($field->id_penjadwalan)) {
                // Button Selesai (status = 5)
                $aksi .= '<button type="button" class="btn btn-sm btn-success btn-selesai-operasi" data-id="'.$field->id_penjadwalan.'" title="Selesai">';
                $aksi .= '<i class="fa fa-check"></i>';
                $aksi .= '</button>';
                // Button Hapus
                $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-hapus-operasi" data-id="'.$field->id_penjadwalan.'" data-perjanjian-id="'.$field->ID.'" title="Hapus">';
                $aksi .= '<i class="fa fa-trash"></i>';
                $aksi .= '</button>';
            } else {
                $aksi .= '<span class="text-muted">-</span>';
            }
            $aksi .= '</div>';
            $sub_array[] = $aksi;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab History/Selesai Operasi
    public function get_history_operasi()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        $hari = $this->input->post('hari') ?: 'All';

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'tgl_operasi', 'kamar_operasi', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'status'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_operasi';

        $result = $this->ModelPenjadwalanOperasi->getHistoryOperasi($start, $length, $searchValue, $tanggal_mulai, $tanggal_akhir, $hari, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            // Status operasi
            $status = '';
            if ($field->status_penjadwalan == 0) {
                $status = '<span class="badge badge-danger">Batal Operasi</span>';
            } elseif ($field->status_penjadwalan == 5) {
                $status = '<span class="badge badge-success">Operasi Selesai</span>';
            }

            $sub_array = array();
            $sub_array[] = $no;
            $tgl_operasi_formatted = '-';
            if (!empty($field->tgl_operasi) && $field->tgl_operasi !== '0000-00-00') {
                $tgl_operasi_formatted = date('d/m/Y', strtotime($field->tgl_operasi));
            }
            $sub_array[] = $tgl_operasi_formatted;
            $sub_array[] = $field->kamar_operasi ?: '-';
            $sub_array[] = $field->nama . ' (' . $field->norm . ')';
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $status;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Get available days in date range
    public function get_available_days()
    {
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');

        $available_days = $this->ModelPenjadwalanOperasi->getAvailableDays($tanggal_mulai, $tanggal_akhir);

        echo json_encode($available_days);
    }

    private function _validation_rules()
    {
        return array(
            array(
                'field' => 'norm',
                'label' => 'Nomor RM',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'id_dokter',
                'label' => 'Dokter',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'tgl_operasi',
                'label' => 'Tanggal operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'kamar_operasi',
                'label' => 'Kamar operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'waktu_operasi',
                'label' => 'Waktu operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'durasi_operasi',
                'label' => 'Durasi operasi',
                'rules' => 'trim|required|numeric',
                'errors' => array(
                    'required' => '%s wajib diisi',
                    'numeric' => '%s harus berupa angka',
                ),
            ),
            array(
                'field' => 'tujuan_rs',
                'label' => 'Tujuan RS',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            )
        );
    }

    // Form Penjadwalan Operasi
    public function form_penjadwalan()
    {
        $id = $this->input->post('id');
        $jenis = $this->input->post('jenis');

        // Get form data
        $dataForm = $this->ModelPenjadwalanOperasi->isiForm($jenis, $id);

        // Get tindakan data
        $tindakanData = array();
        if (!empty($dataForm['id_tpo'])) {
            $tindakanData = $this->ModelPenjadwalanOperasi->ambilTindakan($dataForm['id_tpo']);
        }

        // Get dropdown data
        $data = array(
            'detail' => $dataForm,
            'tindakan' => $tindakanData,
            'dokter' => $this->ModelPenjadwalanOperasi->dokter(),
            'kamar_operasi' => $this->ModelPenjadwalanOperasi->kamar(105090104),
            'dokter_anestesi' => $this->ModelPenjadwalanOperasi->dokter_anestesi(),
            'jenis_anestesi' => $this->ModelPenjadwalanOperasi->referensi(622),
            'list_kelas' => $this->ModelPenjadwalanOperasi->getKelas()
        );

        $this->load->view('PenjadwalanOperasi/FormPenjadwalan', $data);
    }

    // Delete Penjadwalan
    public function hapus_penjadwalan()
    {
        $id = $this->input->post('id');
        $alasan = $this->input->post('alasan');

        if (empty($alasan)) {
            echo json_encode(array('status' => 'error', 'message' => 'Alasan penghapusan wajib diisi'));
            return;
        }

        $result = $this->ModelPenjadwalanOperasi->hapusPenjadwalanOperasi($id, $alasan);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Data berhasil dihapus'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menghapus data'));
        }
    }

    // Hapus Perjanjian Operasi
    public function hapus_perjanjian()
    {
        $id = $this->input->post('id');

        $result = $this->ModelPenjadwalanOperasi->hapusPerjanjianOperasi($id);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Perjanjian berhasil dihapus'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menghapus perjanjian'));
        }
    }

    // Selesai Operasi - Update status = 5
    public function selesai_operasi()
    {
        $id = $this->input->post('id');

        $result = $this->ModelPenjadwalanOperasi->selesaiOperasi($id);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Operasi berhasil diselesaikan'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menyelesaikan operasi'));
        }
    }

    // Hapus Operasi Hari Ini
    public function hapus_operasi()
    {
        $id_penjadwalan = $this->input->post('id_penjadwalan');
        $id_perjanjian = $this->input->post('id_perjanjian');
        $alasan = $this->input->post('alasan');

        if (empty($alasan)) {
            echo json_encode(array('status' => 'error', 'message' => 'Alasan penghapusan wajib diisi'));
            return;
        }

        $result = $this->ModelPenjadwalanOperasi->hapusOperasiHariIni($id_penjadwalan, $id_perjanjian, $alasan);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Operasi berhasil dihapus'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menghapus operasi'));
        }
    }

    // Modal Pilih Tanggal Operasi
    public function modal_pilih_tanggal()
    {
        $this->load->view('PenjadwalanOperasi/ModalPilihTanggal');
    }

    // Get Calendar Data
    public function get_calendar_data()
    {
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');

        $result = $this->ModelPenjadwalanOperasi->getCalendarData($start_date, $end_date);

        echo json_encode($result);
    }

    // Tambah Penjadwalan Baru
    public function tambah_penjadwalan()
    {
        $data = $this->input->post();

        // Update remun_medis.perjanjian
        if (!empty($data['id_perjanjian']) && !empty($data['tgl_operasi'])) {
            $this->db->where('ID', $data['id_perjanjian']);
            $this->db->update('remun_medis.perjanjian', array('TANGGAL' => $data['tgl_operasi']));
        }

        // Update medis.tb_pendaftaran_operasi
        if (!empty($data['id_tpo'])) {
            $update_pendaftaran = array();
            // if (isset($data['slot_operasi'])) {
            //     $update_pendaftaran['slot_operasi'] = $data['slot_operasi'];
            // }
            if (isset($data['kamar_operasi'])) {
                $update_pendaftaran['kamar'] = $data['kamar_operasi'];
            }
            if (isset($data['tgl_operasi'])) {
                $update_pendaftaran['tanggal_operasi'] = $data['tgl_operasi'];
            }
            if (!empty($update_pendaftaran)) {
                $this->db->where('ID', $data['id_tpo']);
                $this->db->update('medis.tb_pendaftaran_operasi', $update_pendaftaran);
            }
        }

        // Prepare data untuk tambah penjadwalan baru
        $kamarOperasi = $data['kamar_operasi'] ?? $data['kamar_operasi_hidden'] ?? null;
        $penjadwalanData = array(
            'id_perjanjian' => $data['id_perjanjian'] ?? null,
            'id_reservasi' => $data['id_reservasi'] ?? null,
            'id_waiting_list_operasi' => $data['id_waiting_list_operasi'] ?? null,
            'kamar_operasi' => $kamarOperasi,
            // 'slot_operasi' => $data['slot_operasi'] ?? null,
            'tujuan_rs' => 16,
            'tgl_rawat' => $data['tgl_rencanaMasuk'] ?? null,
            'tgl_operasi' => $data['tgl_operasi'] ?? null,
            'waktu_operasi' => $data['waktu_operasi'] ?? null,
            'dr_anestesi' => $data['dr_anestesi'] ?? null,
            'jenis_anestesi' => $data['jenis_anestesi'] ?? null,
            'durasi_operasi' => $data['durasi_operasi'] ?? null,
            'created_by' => $this->session->userdata('id_simpel'),
            'created_at' => date('Y-m-d H:i:s')
        );

        // Save penjadwalan operasi
        $result = $this->ModelPenjadwalanOperasi->tambahPenjadwalan($penjadwalanData);
        
        // Update log.log_pendaftaran_operasi if penjadwalan was successful
        if ($result && !empty($data['id_perjanjian'])) {
            $slotOperasiMulti = $data['slot_operasi_multi'] ?? '';
            $kamarOperasiPenjadwalan = $data['kamar_operasi'] ?? $data['kamar_operasi_hidden'] ?? null;
            $tanggalOperasi = $data['tgl_operasi'] ?? null;
            
            if (!empty($slotOperasiMulti) && !empty($kamarOperasiPenjadwalan) && !empty($tanggalOperasi)) {
                $logResult = $this->ModelPenjadwalanOperasi->updateLogPendaftaranOperasi(
                    $data['id_perjanjian'],
                    $kamarOperasiPenjadwalan,
                    $slotOperasiMulti,
                    $tanggalOperasi
                );
                
                if (!$logResult) {
                    log_message('error', 'Failed to update log_pendaftaran_operasi for perjanjian: ' . $data['id_perjanjian']);
                }
            }
        }

        // Update reservasi jika ada id_reservasi
        if ($result && isset($data['id_reservasi']) && !empty($data['id_reservasi'])) {
            $reservasiData = array();
            if (isset($data['idCara_bayar']) && !empty($data['idCara_bayar'])) {
                $reservasiData['id_cara_bayar'] = $data['idCara_bayar'];
            }
            if (isset($data['kelasPasien']) && !empty($data['kelasPasien'])) {
                $reservasiData['id_kelas'] = $data['kelasPasien'];
            }
            if (isset($data['tgl_rencanaMasuk']) && !empty($data['tgl_rencanaMasuk'])) {
                $reservasiData['tgl_rencanaMasuk'] = $data['tgl_rencanaMasuk'];
            }

            if (!empty($reservasiData)) {
                $this->ModelPenjadwalanOperasi->updateReservasi($data['id_reservasi'], $reservasiData);
            }
        }

        if ($result) {
            // WhatsApp notification after successful scheduling (new)
            $this->sendWhatsAppNotification($data, $result, false);
            
            echo json_encode(array('status' => 'success', 'message' => 'Penjadwalan berhasil ditambahkan'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menambahkan penjadwalan'));
        }
    }

    // Update Penjadwalan
    public function update_penjadwalan()
    {
        $data = $this->input->post();

        // Update remun_medis.perjanjian
        if (!empty($data['id_perjanjian']) && !empty($data['tgl_operasi'])) {
            $this->db->where('ID', $data['id_perjanjian']);
            $this->db->update('remun_medis.perjanjian', array('TANGGAL' => $data['tgl_operasi']));
        }

        // Update medis.tb_pendaftaran_operasi
        if (!empty($data['id_tpo'])) {
            $update_pendaftaran = array();
            // if (isset($data['slot_operasi'])) {
            //     $update_pendaftaran['slot_operasi'] = $data['slot_operasi'];
            // }
            if (isset($data['kamar_operasi'])) {
                $update_pendaftaran['kamar'] = $data['kamar_operasi'];
            }
            if (isset($data['tgl_operasi'])) {
                $update_pendaftaran['tanggal_operasi'] = $data['tgl_operasi'];
            }
            if (!empty($update_pendaftaran)) {
                $this->db->where('ID', $data['id_tpo']);
                $this->db->update('medis.tb_pendaftaran_operasi', $update_pendaftaran);
            }
        }

        // Prepare data untuk update penjadwalan
        $kamarOperasi = $data['kamar_operasi'] ?? $data['kamar_operasi_hidden'] ?? null;
        $penjadwalanData = array(
            'id_penjadwalan' => $data['id_penjadwalan'],
            'id_perjanjian' => $data['id_perjanjian'] ?? null,
            'id_reservasi' => $data['id_reservasi'] ?? null,
            'id_waiting_list_operasi' => $data['id_waiting_list_operasi'] ?? null,
            'kamar_operasi' => $kamarOperasi,
            // 'slot_operasi' => $data['slot_operasi'] ?? null,
            'tujuan_rs' => 16,
            'tgl_rawat' => $data['tgl_rencanaMasuk'] ?? null,
            'tgl_operasi' => $data['tgl_operasi'] ?? null,
            'waktu_operasi' => $data['waktu_operasi'] ?? null,
            'dr_anestesi' => $data['dr_anestesi'] ?? null,
            'jenis_anestesi' => $data['jenis_anestesi'] ?? null,
            'durasi_operasi' => $data['durasi_operasi'] ?? null,
            'created_by' => $this->session->userdata('id_simpel'),
            'updated_at' => date('Y-m-d H:i:s')
        );

        // Update penjadwalan operasi
        $result = $this->ModelPenjadwalanOperasi->updatePenjadwalan($penjadwalanData);
        
        // Update log.log_pendaftaran_operasi if update was successful
        if ($result && !empty($data['id_perjanjian'])) {
            $slotOperasiMulti = $data['slot_operasi_multi'] ?? '';
            $kamarOperasiPenjadwalan = $data['kamar_operasi'] ?? $data['kamar_operasi_hidden'] ?? null;
            $tanggalOperasi = $data['tgl_operasi'] ?? null;
            
            if (!empty($slotOperasiMulti) && !empty($kamarOperasiPenjadwalan) && !empty($tanggalOperasi)) {
                $logResult = $this->ModelPenjadwalanOperasi->updateLogPendaftaranOperasi(
                    $data['id_perjanjian'],
                    $kamarOperasiPenjadwalan,
                    $slotOperasiMulti,
                    $tanggalOperasi
                );
                
                if (!$logResult) {
                    log_message('error', 'Failed to update log_pendaftaran_operasi for perjanjian: ' . $data['id_perjanjian']);
                }
            }
        }

        // Update reservasi jika ada id_reservasi
        if ($result && isset($data['id_reservasi']) && !empty($data['id_reservasi'])) {
            $reservasiData = array();
            if (isset($data['idCara_bayar']) && !empty($data['idCara_bayar'])) {
                $reservasiData['id_cara_bayar'] = $data['idCara_bayar'];
            }
            if (isset($data['kelasPasien']) && !empty($data['kelasPasien'])) {
                $reservasiData['id_kelas'] = $data['kelasPasien'];
            }
            if (isset($data['tgl_rencanaMasuk']) && !empty($data['tgl_rencanaMasuk'])) {
                $reservasiData['tgl_rencanaMasuk'] = $data['tgl_rencanaMasuk'];
            }

            if (!empty($reservasiData)) {
                $this->ModelPenjadwalanOperasi->updateReservasi($data['id_reservasi'], $reservasiData);
            }
        }

        if ($result) {
            // WhatsApp notification after successful update (rescheduled)
            $this->sendWhatsAppNotification($data, $result, true);
            
            echo json_encode(array('status' => 'success', 'message' => 'Penjadwalan berhasil diupdate'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal mengupdate penjadwalan'));
        }
    }

    // Get Week Calendar Data - Optimized endpoint
    public function get_week_calendar()
    {
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');

        // Validate date format
        if (!$start_date || !$end_date) {
            echo json_encode(array('error' => 'Start date and end date are required'));
            return;
        }

        // Validate date format Y-m-d
        if (!DateTime::createFromFormat('Y-m-d', $start_date) || !DateTime::createFromFormat('Y-m-d', $end_date)) {
            echo json_encode(array('error' => 'Invalid date format. Use Y-m-d'));
            return;
        }

        // Limit date range to max 14 days for security
        $date1 = new DateTime($start_date);
        $date2 = new DateTime($end_date);
        $diff = $date2->diff($date1)->days;
        
        if ($diff > 14) {
            echo json_encode(array('error' => 'Date range too large. Maximum 14 days allowed'));
            return;
        }

        $rawData = $this->ModelPenjadwalanOperasi->getWeekCalendarData($start_date, $end_date);

        // Get list kamar for structuring response
        $kamarList = $this->ModelPenjadwalanOperasi->getKamarOperasiForCalendar();

        // Structure response for frontend
        $response = array(
            'kamar' => $kamarList,
            'days' => array()
        );

        // Generate 7 days from start_date (including weekends)
        $currentDate = new DateTime($start_date);
        $allDays = array();

        for ($i = 0; $i < 7; $i++) {
            $allDays[] = $currentDate->format('Y-m-d');
            $currentDate->add(new DateInterval('P1D'));
        }

        // Build days array
        foreach ($allDays as $date) {
            $dayData = array(
                'date' => $date,
                'slots' => array()
            );

            // Get all slots for this date from rawData
            if (isset($rawData[$date])) {
                foreach ($rawData[$date] as $slotKey => $slotData) {
                    if (strpos($slotKey, 'slot_') === 0) {
                        // Extract kamar_id and slot from key like 'slot_1_2'
                        $parts = explode('_', $slotKey);
                        if (count($parts) >= 3) {
                            $kamarId = $parts[1];
                            $slotNum = $parts[2];

                            $dayData['slots'][] = array(
                                'kamar_id' => $kamarId,
                                'slot' => intval($slotNum),
                                'jenis' => $slotData['jenis'], // 'scheduled' or 'appointment'
                                'pasien_info' => $slotData['pasien_info'],
                                'nama_dokter' => $slotData['nama_dokter'],
                                'rencana_tindakan_operasi' => $slotData['rencana_tindakan_operasi'],
                                'waktu_selesai' => $slotData['JAM_MULAI'],
                                'waktu_operasi' => $slotData['JAM_AKHIR'],
                                'nama_kamar' => $slotData['nama_kamar']
                            );
                        }
                    }
                }
            }

            $response['days'][] = $dayData;
        }

        echo json_encode($response);
    }
}

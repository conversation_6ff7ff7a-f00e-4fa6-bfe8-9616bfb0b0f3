<div class="card mb-3 shadow-sm">
    <div class="card-header bg-warning text-dark">
        Coding Grouper
    </div>
    <div class="card-body" style="background-color:rgb(247, 246, 234);">

        <div class="card mb-3 shadow-sm">
            <div class="card-header bg-warning text-dark">
                iDRG (ICD-10)
            </div>
            <div class="card-body" style="background-color:rgb(217, 245, 213);">
                <form id="form_idrg" class="p-3 mb-2 border rounded shadow-sm">
                    <input type="hidden" id="nopen" name="nopen" value="<?= $nopen; ?>">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-8">
                            <label for="kodeicd10idrg" class="form-label fw-bold">Kode ICD 10</label>
                            <div class="input-group">
                                <select class="form-control theme-input-style kodeicd10idrg" id="kodeicd10idrg" name="kodeicd10idrg">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row g-3 align-items-center mt-2">
                        <div class="col-md-8">
                            <label for="diagnosaicd10idrg" class="form-label fw-bold">Diagnosa</label>
                            <div class="input-group">
                                <textarea class="form-control diagnosaicd10idrg" id="diagnosaicd10idrg" name="diagnosaicd10idrg" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="col-md-4 mt-4">
                            <button type="button" class="btn btn-primary mt-4 px-4 tombolSimpanidrgicd10">Simpan</button>
                        </div>
                    </div>
                </form>

                <div class="row">
                    <div class="col-md">
                        <table class="table table-bordered table-hover" id="tableidrgicd10" style="width: 100%;">
                            <thead class="table-warning">
                                <tr>
                                    <th center style="width: 5%;">No</th>
                                    <th center style="width: 15%;">Kategori</th>
                                    <th center style="width: 15%;">Kode (ICD 10)</th>
                                    <th center style="width: 65%;">Deskripsi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-3 shadow-sm">
            <div class="card-header bg-warning text-dark">
                iDRG (ICD-9-CM)
            </div>
            <div class="card-body" style="background-color:rgb(230, 240, 255);">
                <form id="form_icd9cm" class="p-3 mb-2 border rounded shadow-sm">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-8">
                            <label for="kodeicd9cmidrg" class="form-label fw-bold">Kode ICD 9 CM</label>
                            <div class="input-group">
                                <select class="form-control theme-input-style kodeicd9cmidrg" id="kodeicd9cmidrg" name="kodeicd9cmidrg">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row g-3 align-items-center mt-2">
                        <div class="col-md-8">
                            <label for="tindakanicd9cmidrg" class="form-label fw-bold">Tindakan</label>
                            <div class="input-group">
                                <textarea class="form-control tindakanicd9cmidrg" id="tindakanicd9cmidrg" name="tindakanicd9cmidrg" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="col-md-4 mt-4">
                            <button type="button" class="btn btn-primary mt-4 px-4 tombolSimpanidrgicd9cm">Simpan</button>
                        </div>
                    </div>
                </form>

                <div class="row">
                    <div class="col-md">
                        <table class="table table-bordered table-hover" id="tableidrgicd9cm" style="width: 100%;">
                            <thead class="table-primary">
                                <tr>
                                    <th center style="width: 5%;">No</th>
                                    <th center style="width: 15%;">Kategori</th>
                                    <th center style="width: 15%;">Kode (ICD 9 CM)</th>
                                    <th center style="width: 55%;">Deskripsi</th>
                                    <th center style="width: 10%;">Jumlah</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-3 shadow-sm">
            <div class="card-header bg-warning text-dark">
                INACBG 
                <button type="button" class="btn btn-primary btn-sm tombolImport">Import From iDRG to INACBG</button>
            </div>
            <div class="card-body" style="background-color:rgb(255, 230, 230);">
                <div class="row">
                    <div class="col-md">
                        <table class="table table-bordered table-hover" id="tableinacbgicd10" style="width: 100%;">
                            <thead class="table-primary">
                                <tr>
                                    <th center style="width: 5%;">No</th>
                                    <th center style="width: 15%;">Kategori</th>
                                    <th center style="width: 15%;">Kode (ICD 10)</th>
                                    <th center style="width: 55%;">Deskripsi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <table class="table table-bordered table-hover" id="tableinacbgicd9cm" style="width: 100%;">
                            <thead class="table-primary">
                                <tr>
                                    <th center style="width: 5%;">No</th>
                                    <th center style="width: 15%;">Kategori</th>
                                    <th center style="width: 15%;">Kode (ICD 9 CM)</th>
                                    <th center style="width: 55%;">Deskripsi</th>
                                    <th center style="width: 10%;">Jumlah</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<div class="modal fade" id="modalJumlah" tabindex="-1" role="dialog" aria-labelledby="modalJumlahLabel" aria-hidden="true" style="background-color: rgba(0,0,0,0.6)!important;">

    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white py-2">
                <h5 class="modal-title" id="modalJumlahLabel">Input Jumlah</h5>
            </div>
            <div class="modal-body">
                <form id="formJumlah">
                    <input type="hidden" name="id_tindakan" id="id_tindakan">
                    <div class="form-group">
                        <label for="jumlah">Jumlah</label>
                        <input type="number" 
                            name="jumlah" 
                            id="jumlah" 
                            class="form-control" 
                            placeholder="Masukkan jumlah" 
                            min="1"
                            step="1"
                            oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+/, '');"
                            onkeydown="return event.keyCode !== 69 && event.keyCode !== 190 && event.keyCode !== 110">
                    </div>
                </form>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-success btn-sm tombolSimpanJumlah">Simpan</button>
            </div>
        </div>
    </div>
</div>


<script>
$(document).ready(function(){

    $('#tableidrgicd9cm').on('click', '.tombolJumlah', function () {
        $('#id_tindakan').val($(this).data('id'));
        $('#jumlah').val($(this).data('jumlah'));
        $('#modalJumlah').modal('show');
    });

    $('.tombolSimpanJumlah').click(function(){
        $.ajax({
            url: '<?php echo base_url('Coder/simpanJumlah')?>',
            type: 'POST',
            data: {
                id_tindakan: $('#id_tindakan').val(),
                jumlah: $('#jumlah').val(),
            },
            success: function(data){
                Swal.fire({
                        title: 'Jumlah berhasil diupdate',
                        type: 'success',
                    }).then(function() {
                        $('#modalJumlah').modal('hide');
                        $('#tableidrgicd9cm').DataTable().ajax.reload(null, false);
                    });
            }
        });
    });

    $('#tableidrgicd10').DataTable({
			"responsive": true,
			"pageLength" : 5,
			"processing": true,
			"serverSide": true,
			"bLengthChange": false,
			"ordering": false,
            "searching": false,
			"order": [],
			"language": {
				"processing": 'Memuat Data...',
				"zeroRecords": "Data Tidak Ditemukan",
				"emptyTable": "Data Tidak Tersedia",
				"loadingRecords": "Harap Tunggu...",
				"paginate": {
					"next":       "Selanjutnya",
					"previous":   "Sebelumnya"
				},
				"info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
				"infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
				"search": "Cari:",
				"lengthMenu": "Tampilkan: _MENU_ Data",
				"infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
			},

			lengthMenu: [[10, 20, 30, 40, 50], [10, 20, 30, 40, 50]],
			ajax: {
				url: '<?php echo base_url('Coder/getdataidrgicd10')?>',
				type: 'POST',
				data: {
					nopen: '<?php echo $nopen; ?>',
				},
			},
			"createdRow": function(row, data, dataIndex) {

			}
		});

        $('#tableidrgicd9cm').DataTable({
			"responsive": true,
			"pageLength" : 5,
			"processing": true,
			"serverSide": true,
			"bLengthChange": false,
			"ordering": false,
            "searching": false,
			"order": [],
			"language": {
				"processing": 'Memuat Data...',
				"zeroRecords": "Data Tidak Ditemukan",
				"emptyTable": "Data Tidak Tersedia",
				"loadingRecords": "Harap Tunggu...",
				"paginate": {
					"next":       "Selanjutnya",
					"previous":   "Sebelumnya"
				},
				"info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
				"infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
				"search": "Cari:",
				"lengthMenu": "Tampilkan: _MENU_ Data",
				"infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
			},

			lengthMenu: [[10, 20, 30, 40, 50], [10, 20, 30, 40, 50]],
			ajax: {
				url: '<?php echo base_url('Coder/getdataidrgicd9cm')?>',
				type: 'POST',
				data: {
					nopen: '<?php echo $nopen; ?>',
				},
			},
			"createdRow": function(row, data, dataIndex) {

			}
		});

    $('#kodeicd10idrg').select2({
        placeholder: '[ Pilih Kode ICD 10 ]',
        width: '100%',
        ajax: {
            dataType: 'json',
            url: "<?php echo base_url('Coder/getIdrg/ICD_10_2010_IM') ?>",
            delay: 250,
            processResults: function (data) {
                return { results: data };
            },
            cache: true
        },
        templateResult: function(data) {
            if (!data.id) return data.text;
            let parts = data.text.split(' - ');
            let kode = `<strong>${parts[0]}</strong>`;
            let deskripsi = parts[1] ? ` - ${parts[1]}` : '';

            if (data.validcode == 0) {
                return $(`<span style="color: red;">${kode}${deskripsi}</span>`);
            }
            
            return $(`<span>${kode}${deskripsi}</span>`);
        },
        templateSelection: function(data) {
            if (!data.id) return data.text;
            let parts = data.text.split(' - ');
            return $(`<span><strong>${parts[0]}</strong>${parts[1] ? ' - ' + parts[1] : ''}</span>`);
        },
        escapeMarkup: function(markup) { return markup; }
    });

    $('#kodeicd10idrg').on('select2:selecting', function(e) {
        let validcode = e.params.args.data.validcode;
        let accpdx = e.params.args.data.accpdx;

        let rowCount = $('#tableidrgicd10').DataTable().rows().count();
        if (validcode == 0) {
            e.preventDefault();
            Swal.fire({
                title: 'Information!',
                text: 'Data ini tidak valid untuk di grouping!',
                type: 'info'
            });
        }

        if (rowCount == 0 && accpdx === 'N') {
            e.preventDefault();
            Swal.fire({
                title: 'Information!',
                text: 'Data ini tidak bisa dijadikan primary!',
                icon: 'info'
            });
            return;
        }
    });

    $('#kodeicd9cmidrg').select2({
        placeholder: '[ Pilih Kode ICD 9 CM ]',
        width: '100%',
        ajax: {
            dataType: 'json',
            url: "<?php echo base_url('Coder/getIdrg/ICD_9CM_2010_IM') ?>",
            delay: 250,
            processResults: function (data) {
                return { results: data };
            },
            cache: true
        },
        templateResult: function(data) {
            if (!data.id) return data.text;
            let parts = data.text.split(' - ');
            let kode = `<strong>${parts[0]}</strong>`;
            let deskripsi = parts[1] ? ` - ${parts[1]}` : '';

            if (data.validcode == 0) {
                return $(`<span style="color: red;">${kode}${deskripsi}</span>`);
            }

            return $(`<span>${kode}${deskripsi}</span>`);
        },
        templateSelection: function(data) {
            if (!data.id) return data.text;
            let parts = data.text.split(' - ');
            return $(`<span><strong>${parts[0]}</strong>${parts[1] ? ' - ' + parts[1] : ''}</span>`);
        },
        escapeMarkup: function(markup) { return markup; }
    });

    $('#kodeicd9cmidrg').on('select2:selecting', function(e) {
        let validcode = e.params.args.data.validcode;
        if (validcode == 0) {
            e.preventDefault();
            Swal.fire({
                title: 'Information!',
                text: 'Data ini tidak valid untuk di grouping!',
                type: 'info'
            });
        }
    });

    $('#tableidrgicd10').on('click', '.hapusIcd10', function(){
        let id = $(this).data('id');
        let nopen = $(this).data('nopen');
        $.ajax({
            url: "<?php echo base_url('Coder/hapusIdrg/ICD_10_2010_IM') ?>",
            type: "POST",
            dataType: 'json',
            data: {
                id: id,
                nopen: nopen,
            },
            success: function(data) {
                if (data.status === 'success') {
                    Swal.fire({
                        title: data.message,
                        type: 'success',
                    }).then(function() {
                        $('#tableidrgicd10').DataTable().ajax.reload(null, false);
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        type: 'error'
                    });
                }
            }
        });
    });

    $('#tableidrgicd9cm').on('click', '.hapusIcd9cm', function(){
        let id = $(this).data('id');
        let nopen = $(this).data('nopen');
        $.ajax({
            url: "<?php echo base_url('Coder/hapusIdrg/ICD_9CM_2010_IM') ?>",
            type: "POST",
            dataType: 'json',
            data: {
                id: id,
                nopen: nopen,
            },
            success: function(data) {
                if (data.status === 'success') {
                    Swal.fire({
                        title: data.message,
                        type: 'success',
                    }).then(function() {
                        $('#tableidrgicd9cm').DataTable().ajax.reload(null, false);
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        type: 'error'
                    });
                }
            }
        });
    });

    $('.tombolSimpanidrgicd10').click(function(){
        let kodeicd10idrg = $('#kodeicd10idrg').val();
        let nopen = $('#nopen').val();
        let diagnosa = $('.diagnosaicd10idrg').val();
        $.ajax({
            url: "<?php echo base_url('Coder/simpanIdrgIcd/ICD_10_2010_IM') ?>",
            type: "POST",
            dataType: 'json',
            data: {
                kodeicd10idrg: kodeicd10idrg,
                nopen: nopen,
                diagnosa: diagnosa,
            },
            success: function(data) {
                if (data.status === 'success') {
                        Swal.fire({
                            title: 'Data berhasil ditambahkan',
                            type: 'success',
                        }).then(function() {
                            // location.reload();
                            $('#tableidrgicd10').DataTable().ajax.reload(null, false);
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message,
                            type: 'error'
                        });
                    }
            }
        });
    });

    $('.tombolSimpanidrgicd9cm').click(function(){
        let kodeicd9cmidrg = $('#kodeicd9cmidrg').val();
        let nopen = $('#nopen').val();
        let tindakan = $('.tindakanicd9cmidrg').val();
        $.ajax({
            url: "<?php echo base_url('Coder/simpanIdrgIcd/ICD_9CM_2010_IM') ?>",
            type: "POST",
            dataType: 'json',
            data: {
                kodeicd9cmidrg: kodeicd9cmidrg,
                nopen: nopen,
                tindakan: tindakan,
            },
            success: function(data) {
                if (data.status === 'success') {
                        Swal.fire({
                            title: 'Data berhasil ditambahkan',
                            type: 'success',
                        }).then(function() {
                            // location.reload();
                            $('#tableidrgicd9cm').DataTable().ajax.reload(null, false);
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Gagal menambahkan data',
                            type: 'error'
                        });
                    }
            }
        });
    });

});
</script>

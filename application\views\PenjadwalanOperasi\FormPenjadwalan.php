<style>
    /* Green shadow for editable fields */
    #formPenjadwalan .form-control:not([readonly]),
    #formPenjadwalan .form-control.flatpickr-input:not(:disabled),
    #formPenjadwalan .select2-container--default .select2-selection--single,
    #formPenjadwalan .select2-container--default .select2-selection--multiple {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    #tglRawatPenjadwalan[readonly] {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    /* Style untuk altInput Flatpickr juga */
    .flatpickr-input[readonly] {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
        background-color: #fff !important;
    }

    /* Unified style for input groups dengan field readonly */
    .input-group.custom-green {
        border: 1.5px solid #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40,167,69,0.25) !important;
        border-radius: .25rem;
        overflow: hidden;
        display: flex;
    }
    .input-group.custom-green .form-control,
    .input-group.custom-green .input-group-append .btn {
        border: none !important;
        box-shadow: none !important;
    }

    .clearable-input {
        position: relative;
        display: inline-block;
        width: 100%;
    }
    .clear-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #999;
        font-size: 20px;
        line-height: 1;
        display: none; /* Initially hidden */
        z-index: 10;
    }
    
    /* Minimize button style */
    .minimize-btn {
        position: absolute;
        left: 10px;
        top: 10px;
        z-index: 10;
    }
    
    /* Tindakan section styling */
    .tindakan-section-header {
        position: relative;
        padding: 0.75rem 1.25rem;
    }
</style>
<!-- Modal Form Penjadwalan Operasi -->
<div class="modal fade" id="modalFormPenjadwalan" tabindex="-1" role="dialog" aria-labelledby="modalFormPenjadwalanLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-xl" role="document" style="max-height: 90vh; margin: 1.75rem auto;">
        <div class="modal-content" style="height: 90vh; display: flex; flex-direction: column;">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modalFormPenjadwalanLabel">
                    <i class="mdi mdi-calendar-plus"></i> <?= isset($detail['id_penjadwalan']) ? 'Ubah' : 'Buat' ?> Penjadwalan Operasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="flex: 1; overflow-y: auto; padding: 20px;">
                <form id="formPenjadwalan" method="post">
            <!-- Hidden Fields -->
            <input type="hidden" name="id_waiting_list_operasi" value="<?= $detail['id_waiting_list_operasi'] ?? null ?>">
            <input type="hidden" name="id_penjadwalan" value="<?= $detail['id_penjadwalan'] ?? null ?>">
            <input type="hidden" name="id_perjanjian" value="<?= $detail['id_perjanjian'] ?? null ?>">
            <input type="hidden" name="id_reservasi" value="<?= $detail['id_reservasi'] ?? null ?>">
            <input type="hidden" name="id_tpo" id="idPendaftaranPenjadwalan" value="<?= $detail['id_tpo'] ?? null ?>">
            <input type="hidden" name="id_jk" id="jkPenjadwalan" value="<?= $detail['id_jk'] ?? null ?>">
            <!-- <input type="text" name="slot_operasi" id="slotOperasiPenjadwalan" value="<?= $detail['slot_operasi'] ?? null ?>"> -->
            <input type="hidden" name="kamar_operasi_hidden" id="kamarOperasiHidden" value="<?= $detail['kamar_operasi'] ?? '' ?>">
            <input type="hidden" name="slot_operasi_multi" id="slot-operasi-pendaftaran-pra-operasi" value="<?= $detail['slot_operation_log'] ?? '' ?>">
            <input type="hidden" name="nohp" id="inputNoHp" value="<?= $detail['no_telp'] ?? '' ?>">
            <!-- Container utama untuk semua field, agar layout dinamis -->
            <div class="row">
                <!-- Baris 1 -->
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="mrPenjadwalan" class="form-label">Nomor RM</label>
                        <input type="text" name="norm" id="mrPenjadwalan" class="form-control" value="<?= $detail['norm'] ?? null ?>" readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="namaPenjadwalan" class="form-label">Nama</label>
                        <input type="text" name="nama" id="namaPenjadwalan" class="form-control" value="<?= $detail['nama'] ?? null ?>" readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="teleponPenjadwalan" class="form-label">Nomor telepon</label>
                        <input type="text" name="no_telp" id="teleponPenjadwalan" class="form-control" value="<?= $detail['no_telp'] ?? null ?>" readonly>
                    </div>
                </div>
                <!-- Baris 2 -->
              <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="pendaftaran" class="form-label">Pendaftaran pra operasi</label>
                        <?php
                        // Kumpulkan data yang ada saja
                        $parts = [];
                        if (!empty($detail['nokun'])) {
                            $parts[] = $detail['nokun'];
                        }
                        if (!empty($detail['diagnosa_medis'])) {
                            $parts[] = $detail['diagnosa_medis'];
                        }
                        if (!empty($detail['tgl_operasi'])) {
                            $parts[] = date('d/m/Y', strtotime($detail['tgl_operasi']));
                        }

                        // Gabungkan dengan pemisah " - "
                        $pendaftaranValue = implode(' - ', $parts);
                        ?>
                        <input type="text"
                            name="pendaftaran"
                            id="pendaftaran"
                            class="form-control"
                            value="<?= $pendaftaranValue ?>"
                            readonly>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="diagnosisPenjadwalan" class="form-label">Diagnosis</label>
                        <textarea name="diagnosis" id="diagnosisPenjadwalan" class="form-control" rows="1" readonly><?= $detail['diagnosis'] ?? null ?></textarea>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="tindakanPenjadwalan" class="form-label">Tindakan</label>
                        <i class="fa fa-eye ml-2 text-primary" id="toggleTindakanDetails" style="cursor: pointer;"></i>
                        <textarea name="tindakan" id="tindakanPenjadwalan" class="form-control" rows="1" readonly><?= $detail['tindakan'] ?? null ?></textarea>
                    </div>
                </div>
            </div> <!-- End of row -->
            
            <!-- Collapsible Tindakan Details Section - Moved outside of the previous row and made full width -->
            <div class="row">
                <div class="col-md-12">
                    <div id="tindakanDetailsSection" class="collapse mt-3">
                        <!-- Dokter Bedah Utama Section -->
                        <div class="card mb-3">
                            <div class="card-header bg-light tindakan-section-header">
                                <button type="button" class="btn btn-sm btn-outline-secondary minimize-btn" data-target="mainSurgeonSection">
                                    <i class="fa <?= !isset($tindakan['main_surgeon']['rencana_tindakan_operasi']) ? 'fa-plus' : 'fa-minus' ?>"></i>
                                </button>
                                <h6 class="mb-0 text-center"> Dokter Bedah Utama</h6>
                            </div>
                            <div id="mainSurgeonSection" class="card-body" <?= !isset($tindakan['main_surgeon']['rencana_tindakan_operasi']) ? 'style="display:none;"' : '' ?>>
                                <div class="form-group">
                                    <label>Dokter Bedah Utama 1</label>
                                    <input type="text" class="form-control" value="<?= isset($tindakan['main_surgeon']['nama_dokter']) ? $tindakan['main_surgeon']['nama_dokter'] : '' ?>" readonly>
                                </div>
                                <div class="form-group">
                                    <label class="col-form-label col-md-12">
                                        <strong>Rencana Tindakan Operasi untuk Dokter Utama</strong><br>
                                        <small class="text-muted">(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                                    </label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <textarea class="form-control" rows="3" readonly><?php 
                                                if (isset($tindakan['main_surgeon']['rencana_tindakan_operasi'])) {
                                                    echo $tindakan['main_surgeon']['rencana_tindakan_operasi'];
                                                } else {
                                                    echo '';
                                                }
                                            ?></textarea>
                                        </div>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control"  value="<?= isset($tindakan['main_surgeon']['tindakan']) ? $tindakan['main_surgeon']['tindakan'] : '' ?>" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if (!empty($tindakan['log_tindakan'])): ?>
                                <div class="form-group">
                                    <textarea class="form-control" rows="3" readonly><?php 
                                        $logTindakanText = '';
                                        foreach ($tindakan['log_tindakan'] as $log) {
                                            if (!empty($log['rencana_tindakan_log']) && !empty($log['tindakan'])) {
                                                $logTindakanText .= $log['rencana_tindakan_log'] . ' ' . $log['tindakan'] . "\n";
                                            }
                                        }
                                        echo trim($logTindakanText);
                                    ?></textarea>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Dokter Bedah Lain Section -->
                        <div class="card">
                            <div class="card-header bg-light tindakan-section-header">
                                <button type="button" class="btn btn-sm btn-outline-secondary minimize-btn" data-target="assistantSurgeonsSection">
                                    <i class="fa <?= empty($tindakan['assistant_surgeons']) ? 'fa-plus' : 'fa-minus' ?>"></i>
                                </button>
                                <h6 class="mb-0 text-center">Dokter Bedah Lain</h6>
                            </div>
                            <div id="assistantSurgeonsSection" class="card-body" <?= empty($tindakan['assistant_surgeons']) ? 'style="display:none;"' : '' ?>>
                                <?php if (!empty($tindakan['assistant_surgeons'])): ?>
                                    <?php foreach ($tindakan['assistant_surgeons'] as $index => $assistant): ?>
                                    <div class="form-group">
                                        <label>Dokter Bedah Lain <?= $index + 1 ?></label>
                                        <input type="text" class="form-control" value="<?= isset($assistant['nama_dokter_lain']) ? $assistant['nama_dokter_lain'] : '' ?>" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-form-label col-md-12">
                                            <strong>Rencana Tindakan Operasi untuk Dokter Lain</strong><br>
                                            <small class="text-muted">(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                                        </label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <textarea class="form-control" rows="3" readonly><?php 
                                                    if (isset($assistant['rencana_tindakan'])) {
                                                        echo $assistant['rencana_tindakan'];
                                                    }
                                                ?></textarea>
                                            </div>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control"  value="<?= isset($assistant['tindakan']) ? $assistant['tindakan'] : '' ?>" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="form-group">
                                        <label>Dokter Bedah Lain 1</label>
                                        <input type="text" class="form-control" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-form-label col-md-12">
                                            <strong>Rencana Tindakan Operasi untuk Dokter Lain</strong><br>
                                            <small class="text-muted">(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                                        </label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <textarea class="form-control" rows="3" readonly></textarea>
                                            </div>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control"  value="" readonly>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Continue with the rest of the form -->
            <div class="row">
                <!-- Baris 3 -->
                <?php if (isset($detail['id_reservasi']) && !empty($detail['id_reservasi'])): ?>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="tglRawatPenjadwalan" class="form-label">Tanggal Rawat</label>
                        <div class="input-group custom-green">
                        <input type="date" name="tgl_rencanaMasuk" id="tglRawatPenjadwalan" class="form-control" value="<?= $detail['tgl_rawat'] ?? '' ?>" readonly>
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button" id="ubahTglRawatBtn">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="tanggalOperasiPenjadwalan" class="form-label">Tanggal operasi</label>
                        <div class="input-group custom-green">
                            <input type="date" name="tgl_operasi" id="tanggalOperasiPenjadwalan" class="form-control" value="<?= $detail['tgl_operasi'] ?>">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button" id="pilihTanggalOperasiPenjadwalan">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="kamarOperasiPenjadwalan" class="form-label">Kamar operasi</label>
                        <select name="kamar_operasi" id="kamarOperasiPenjadwalan" class="form-control" style="width: 100%;">
                            <option value=""></option>
                            <?php foreach ($kamar_operasi as $ko): ?>
                                <option value="<?= $ko['id'] ?>" <?= isset($detail['kamar_operasi']) && $detail['kamar_operasi'] == $ko['id'] ? 'selected' : '' ?>><?= $ko['nama'] ?></option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
                <!-- Baris 4 -->
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="waktuOperasiPenjadwalan" class="form-label">Waktu operasi dimulai</label>
                        <input type="time" name="waktu_operasi" id="waktuOperasiPenjadwalan" class="form-control" value="<?= $detail['waktu_operasi'] ?? null ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="durasiOperasiPenjadwalan" class="form-label">Durasi operasi (menit)</label>
                        <input type="number" name="durasi_operasi" id="durasiOperasiPenjadwalan" class="form-control" value="<?= $detail['durasi_operasi'] ?? null ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="dokterAnestesiPenjadwalan" class="form-label">Dokter anestesi</label>
                        <select name="dr_anestesi" id="dokterAnestesiPenjadwalan" class="form-control" style="width: 100%;">
                            <option value="">Pilih Dokter Anestesi</option>
                            <?php foreach ($dokter_anestesi as $da): ?>
                                <option value="<?= $da['id_dokter'] ?>" <?= isset($detail['dr_anestesi']) && $detail['dr_anestesi'] == $da['id_dokter'] ? 'selected' : '' ?>>
                                    <?= $da['dokter'] . ' - ' . $da['smf'] ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
                <!-- Baris 5 -->
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="jenisAnestesiPenjadwalan" class="form-label">Jenis anestesi</label>
                        <select name="jenis_anestesi" id="jenisAnestesiPenjadwalan" class="form-control" style="width: 100%;">
                            <option value=""> Pilih Jenis Anestesi</option>
                            <?php foreach ($jenis_anestesi as $ja): ?>
                                <option value="<?= $ja['id_variabel'] ?>" <?= isset($detail['jenis_anestesi']) && $detail['jenis_anestesi'] == $ja['id_variabel'] ? 'selected' : '' ?>>
                                    <?= $ja['variabel'] ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
                 <!-- Dokter -->
                 <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="dokterPenjadwalan" class="form-label">Dokter</label>
                        <select name="id_dokter" id="dokterPenjadwalan" class="form-control" style="width: 100%;">
                            <option value="">Pilih Dokter</option>
                            <?php foreach ($dokter as $d): ?>
                                <option value="<?= $d['id_dokter'] ?>" <?= isset($detail['id_dokter']) && $detail['id_dokter'] == $d['id_dokter'] ? 'selected' : '' ?>>
                                    <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
            <!-- Conditional Fields, diletakkan di dalam row utama -->
            <?php if (isset($detail['id_reservasi']) && !empty($detail['id_reservasi'])): ?>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="cara_bayar" class="form-label">Cara Bayar</label>
                        <div class="clearable-input">
                            <input type="text" class="form-control validate-input" name="cara_bayar" id="cara_bayar" value="<?= $detail['cara_bayar'] ?? '' ?>" autocomplete="off">
                            <span class="clear-icon">&times;</span>
                        </div>
                        <input type="hidden" name="idCara_bayar" id="idCara_bayar" value="<?= $detail['id_cara_bayar'] ?? '' ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="kelasPasien" class="form-label">Kelas</label>
                        <select class="form-control validate-input" name="kelasPasien" id="kelasPasien" style="width: 100%;">
                            <option value="">Pilih Kelas</option>
                            <?php if (isset($list_kelas)): ?>
                                <?php foreach ($list_kelas as $kelas): ?>
                                    <option value="<?= $kelas['ID'] ?>" <?= isset($detail['id_kelas']) && $detail['id_kelas'] == $kelas['ID'] ? 'selected' : '' ?>>
                                        <?= $kelas['DESKRIPSI'] ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>
            <?php endif; ?>

            </div> <!-- Penutup .row utama -->
                </form>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="konfirmasiCheck">
                    <label class="form-check-label" for="konfirmasiCheck">
                        Saya sudah mengkonfirmasi ke IBS dan pihak asuransi
                    </label>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> Batal
                    </button>
                    <button type="submit" form="formPenjadwalan" id="btnKonfirmasi" class="btn btn-primary" disabled>
                        <i class="fa fa-check"></i> Konfirmasi
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>




<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns with error handling
    try {
        if (typeof $.fn.select2 !== 'undefined') {
            // Initialize dokter anestesi
            var $dokterAnestesi = $('#dokterAnestesiPenjadwalan').select2({
                placeholder: 'Pilih dokter anestesi',
                dropdownParent: $('#modalFormPenjadwalan'),
                allowClear: true
            });

            // Set value if exists
            var dokterAnestesiVal = <?= isset($detail['dr_anestesi']) ? json_encode($detail['dr_anestesi']) : 'null' ?>;
            if (dokterAnestesiVal !== null) {
                $dokterAnestesi.val(dokterAnestesiVal);
                if (typeof $dokterAnestesi.trigger === 'function') {
                    $dokterAnestesi.trigger('change');
                }
            }

            // Initialize jenis anestesi
            var $jenisAnestesi = $('#jenisAnestesiPenjadwalan').select2({
                placeholder: 'Pilih jenis anestesi',
                dropdownParent: $('#modalFormPenjadwalan'),
                allowClear: true
            });

            // Set value if exists
            var jenisAnestesiVal = <?= isset($detail['jenis_anestesi']) ? json_encode($detail['jenis_anestesi']) : 'null' ?>;
            if (jenisAnestesiVal !== null) {
                $jenisAnestesi.val(jenisAnestesiVal);
                if (typeof $jenisAnestesi.trigger === 'function') {
                    $jenisAnestesi.trigger('change');
                }
            }

            $('#dokterPenjadwalan').select2({
                placeholder: 'Pilih dokter',
                dropdownParent: $('#modalFormPenjadwalan'),
                allowClear: true
            });

            $('#kamarOperasiPenjadwalan').select2({
                placeholder: 'Pilih kamar operasi',
                dropdownParent: $('#modalFormPenjadwalan'),
                allowClear: true
            });
        } else {
            console.warn('Select2 library not loaded properly');
        }
    } catch (error) {
        console.error('Error initializing Select2:', error);
    }

    // Logic for confirmation checkbox
    $('#konfirmasiCheck').on('change', function() {
        if ($(this).is(':checked')) {
            $('#btnKonfirmasi').prop('disabled', false);
        } else {
            $('#btnKonfirmasi').prop('disabled', true);
        }
    });

    // --- Logic for clearable input on #cara_bayar ---
    function toggleClearIcon() {
        var input = $('#cara_bayar');
        var clearIcon = input.parent().find('.clear-icon');
        if (input.val() && input.val().length > 0) {
            clearIcon.show();
        } else {
            clearIcon.hide();
        }
    }
    toggleClearIcon();
    $('#cara_bayar').on('keyup input', function() {
        toggleClearIcon();
        if ($(this).val() === '') {
            $('#idCara_bayar').val('');
        }
    });
    $(document).on('click', '.clear-icon', function() {
        var input = $(this).siblings('input');
        input.val('').trigger('input');
        $('#idCara_bayar').val('');
        input.focus();
    });

    // Initialize Select2 for cara bayar dan kelas
    <?php if (isset($detail['id_reservasi']) && !empty($detail['id_reservasi'])): ?>
    $('#kelasPasien').select2({
        placeholder: 'Pilih kelas',
        dropdownParent: $('#modalFormPenjadwalan')
    });
    $("#cara_bayar").autocomplete({
        serviceUrl: "<?= base_url('Admision/cara_bayar'); ?>",
        paramName: "q",
        dataType: "json",
        minChars: 1,
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return { value: dataItem.value, data: dataItem.id };
                })
            };
        },
        onSelect: function (suggestion) {
            $("#idCara_bayar").val(suggestion.data);
        }
    });
    <?php endif; ?>

    // Pilih Tanggal Operasi button handler
    $('#pilihTanggalOperasiPenjadwalan').click(function() {
        $.ajax({
            url: '<?php echo base_url('PenjadwalanOperasi/modal_pilih_tanggal'); ?>',
            type: 'POST',
            success: function(response) {
                $('#modalPilihTanggalOperasiBody').html(response);
                $('#modalPilihTanggalOperasi').modal('show');
            },
            error: function() {
                Swal.fire('Error', 'Gagal memuat kalender', 'error');
            }
        });
    });

    // Fix modal scroll issue when ModalPilihTanggal is closed
    $('#modalPilihTanggalOperasi').on('hidden.bs.modal', function () {
        // Re-enable scrolling on body
        $('body').removeClass('modal-open').css({
            'overflow': '',
            'padding-right': ''
        });

        // Ensure FormPenjadwalan modal remains scrollable
        $('#modalFormPenjadwalan').css({
            'overflow-y': 'auto'
        });

        // Re-add modal-open class if FormPenjadwalan is still open
        if ($('#modalFormPenjadwalan').hasClass('show')) {
            $('body').addClass('modal-open');
        }
    });

    // Ensure proper modal behavior on show
    $('#modalFormPenjadwalan').on('shown.bs.modal', function () {
        $(this).find('.modal-body').css('overflow-y', 'auto');
    });

    // Form submit handler
    $('#formPenjadwalan').submit(function(e) {
        e.preventDefault();
        var formData = $(this).serialize();

        // Tentukan URL berdasarkan apakah ini tambah atau update
        var idPenjadwalan = $('#formPenjadwalan input[name="id_penjadwalan"]').val();
        var url = '';

        if (idPenjadwalan && idPenjadwalan.trim() !== '' && idPenjadwalan !== 'null') {
            // Update existing
            url = '<?php echo base_url('PenjadwalanOperasi/update_penjadwalan'); ?>';
        } else {
            // Tambah baru
            url = '<?php echo base_url('PenjadwalanOperasi/tambah_penjadwalan'); ?>';
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    Swal.fire('Berhasil', response.message, 'success').then(() => {
                        $('#modalFormPenjadwalan').modal('hide');
                        if (typeof window.tableDaftarPerjanjian !== 'undefined' && window.tableDaftarPerjanjian) {
                            window.tableDaftarPerjanjian.ajax.reload();
                        }
                        // if (typeof tableOperasiHariIni !== 'undefined' && tableOperasiHariIni) {
                        //     tableOperasiHariIni.ajax.reload();
                        // }
                    });
                } else {
                    Swal.fire('Error', response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                Swal.fire('Error', 'Terjadi kesalahan saat menyimpan data', 'error');
            }
        });
    });

    // Toggle tindakan details section
    $('#toggleTindakanDetails').on('click', function() {
        var isVisible = $('#tindakanDetailsSection').is(':visible');
        $('#tindakanDetailsSection').collapse('toggle');
        
        // Just toggle between text-primary and text-secondary
        $(this).toggleClass('text-primary text-secondary');
    });

    // Minimize sections - using event delegation for dynamically created elements
    $(document).on('click', '.minimize-btn', function() {
        var target = $(this).data('target');
        var isVisible = $('#' + target).is(':visible');
        $('#' + target).slideToggle();
        
        // Update icon based on visibility after toggle
        if(isVisible) {
            // Section is being hidden, show plus icon
            $(this).find('i').removeClass('fa-minus').addClass('fa-plus');
        } else {
            // Section is being shown, show minus icon
            $(this).find('i').removeClass('fa-plus').addClass('fa-minus');
        }
    });

    // --- Handler untuk input date native ---
    $('#tglRawatPenjadwalan').on('change', function() {
        toastr.success('Tanggal rawat berhasil diubah menjadi ' + $(this).val(), 'Berhasil', {
            closeButton: true,
            progressBar: true,
            timeOut: 3000,
            positionClass: 'toast-top-right',
            closeHtml: '<button style="color: #fff;">&times;</button>'
        });
        $(this).prop('readonly', true);
    });

    // Button click handler to open the date picker
    $('#ubahTglRawatBtn').on('click', function() {
        var namaPasien = $('#namaPenjadwalan').val();
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin mengubah tanggal rawat untuk pasien ' + namaPasien + '?',
            type: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya',
            cancelButtonText: 'Tidak'
        }).then((result) => {
            if (result.value) {
                var element = document.querySelector("#tglRawatPenjadwalan");
                element.readOnly = false;
            }
        });
    });

    var timePicker = flatpickr("#waktuOperasiPenjadwalan", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
        time_24hr: true,
        minuteIncrement: 15,
    });

    // Show modal when loaded
    $('#modalFormPenjadwalan').modal('show');
});
</script>
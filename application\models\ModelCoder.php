<?php 
defined('BASEPATH') OR exit('No direct script access allowed');

class ModelCoder extends CI_Model {
  
  // private $db1;
  
  public function __construct(){
    $this->load->database();
    // $this->db1 = $this->load->database('real', true);
  }
  
public function dataListcosting()
  {
    $query = $this->db->query("
      SELECT det.*, det.ID ID_DETAIL, det.STATUS statuscek, has.DESKRIPSI
      FROM db_rekammedis.tb_coding cod 
      LEFT JOIN db_rekammedis.tb_coding_detail det ON cod.ID = det.ID_CODING
      LEFT JOIN db_rekammedis.tb_master_hasil has ON has.ID = det.HASIL_RAD
      ORDER BY det.ID ASC
      ");
    return $query;
  }
  public function getstatus(){
    $query = $this->db
    ->select('*')
    ->from('db_rekammedis.referensi')
    ->where('JENIS', '16')
    ->get();
    return $query->result_array();

  }


//   public function dataListcosting_admin(){
// //     $subquery = $this->db->select('kun.NOPEN, MAX(rad.STATUS) AS HASIL_RAD')
// //     ->from('pendaftaran.kunjungan kun')
// //     ->join('layanan.tindakan_medis tin', 'tin.KUNJUNGAN = kun.NOMOR', 'inner')
// //     ->join('layanan.hasil_rad rad', 'rad.TINDAKAN_MEDIS = tin.ID', 'inner')
// //     ->group_by('kun.NOPEN')
// //     ->get_compiled_select();
// //     $this->db->select('
// //     dr.DESKRIPSI AS statusSEP,
// //     top.nama_lengkap AS COSTING,
// //     DATE_FORMAT(det.DONE_DATE, "%d-%m-%Y") AS DONE_DATE,
// //     det.ID,
// //     cod.TANGGAL_TARIK,
// //     det.NORM,
// //     det.NAMA_PASIEN,
// //     det.NOPEN,
// //     top2.nama_lengkap AS nama,
// //     un.DESKRIPSI,
// //     det.TANGGAL_PENDAFTARAN,
// //     det.SEP,
// //     rad_subquery.HASIL_RAD,
// //     det.STATUS
// // ');
// // $this->db->from('db_rekammedis.tb_coding_detail det');
// // $this->db->join('db_rekammedis.tb_operator top', 'top.ID = det.DONE_BY', 'left');
// // $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
// // $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
// // $this->db->join('db_rekammedis.unitlayanan un', 'un.ID_SIMPEL = cod.ID_RUANGAN', 'left');
// // $this->db->join('db_rekammedis.tb_operator top2', 'top2.ID = cod.ID_PETUGAS', 'left');
// // $this->db->join("($subquery) rad_subquery", 'rad_subquery.NOPEN = det.NOPEN', 'left');

// // $query = $this->db->get();
// // return $query;

//     $this->db->select("
//         dr.DESKRIPSI AS statusSEP,
//         top.nama_lengkap AS COSTING,
//         DATE_FORMAT(det.DONE_DATE, '%d-%m-%Y') AS DONE_DATE,
//         det.ID,
//         cod.TANGGAL_TARIK,
//         det.NORM,
//         det.NAMA_PASIEN,
//         det.NOPEN,
//         top2.nama_lengkap AS nama,
//         mr.DESKRIPSI,
//         det.TANGGAL_PENDAFTARAN,
//         det.SEP,
//         det.HASIL_RAD,
//         det.STATUS
//     ");
//     $this->db->from('db_rekammedis.tb_coding_detail det');    
//     $this->db->join('db_rekammedis.tb_operator top', 'top.ID = det.DONE_BY', 'left');
//     $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
//     $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
//     $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
//     // $this->db->join('db_rekammedis.unitlayanan un', 'un.ID_SIMPEL = cod.ID_RUANGAN', 'left');
//     $this->db->join('db_rekammedis.tb_operator top2', 'top2.ID = cod.ID_PETUGAS', 'left');
//     $this->db->where_in('det.STATUS', [1, 2]);
//     $query = $this->db->get();
//     return $query;
//   }
public function dataListcosting_admin($start, $length, $search_value, $tanggal = null)
{
    // Query untuk mendapatkan jumlah total data
    $this->db->select("COUNT(*) as total");
    $this->db->from('db_rekammedis.tb_coding_detail det');
    // $this->db->join('db_rekammedis.tb_operator top', 'top.ID = det.DONE_BY', 'left');
    // $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
    // $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
    // $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
    // $this->db->join('db_rekammedis.tb_operator top2', 'top2.ID = cod.ID_PETUGAS', 'left');
    // $this->db->where_in('det.STATUS', [1, 2]);
    // $this->db->where('det.STATUS !=', 0);
    $this->db->where('det.STATUS =', 1);

    
    $query_total = $this->db->get()->row();
    $recordsTotal = $query_total->total;
      if (!empty($tanggal)) {
        $this->db->where('DATE(det.TANGGAL_LAYANAN)', $tanggal);
      }


    
    $this->db->select("
        dr.DESKRIPSI AS statusSEP,
        top.nama_lengkap AS COSTING,
        DATE_FORMAT(det.DONE_DATE, '%d-%m-%Y') AS DONE_DATE,
        det.ID,
        cod.TANGGAL_TARIK,
        det.NORM,
        det.NAMA_PASIEN,
        det.NOPEN,
        top2.nama_lengkap AS nama,
        top2.id as id_petugas,
        mr.DESKRIPSI,
        det.TANGGAL_PENDAFTARAN,
        det.SEP,
        det.HASIL_RAD,
        det.STATUS
    ");
    $this->db->from('db_rekammedis.tb_coding_detail det');    
    $this->db->join('db_rekammedis.tb_operator top', 'top.ID = det.DONE_BY', 'left');
    $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
    $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
    $this->db->join('db_rekammedis.tb_operator top2', 'top2.ID = det.ID_PETUGAS', 'left');
    $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
    // $this->db->where_in('det.STATUS', [1, 2]);
    $this->db->where('det.STATUS =', 1);
    $this->db->order_by('cod.TANGGAL_TARIK', 'DESC');

    if (!empty($search_value)) {
        $this->db->group_start();
        $this->db->like("det.NAMA_PASIEN", $search_value);
        $this->db->or_like("det.NORM", $search_value);
        $this->db->or_like("det.SEP", $search_value);
        $this->db->or_like("top2.nama_lengkap ", $search_value);
        $this->db->group_end();
    }

    $filtered_query = clone $this->db;
    $filtered_result = $filtered_query->get()->num_rows();

    // Limit dan Offset
    $this->db->limit($length, $start);
    $query = $this->db->get();

    return [
        'data' => $query,
        'recordsTotal' => $recordsTotal,
        'recordsFiltered' => $filtered_result
    ];
}


  public function getCoding($id){
    $this->db->select("NORM,NAMA_PASIEN,SEP,STATSEP,DPJP");
    $this->db->from("db_rekammedis.tb_coding_detail");
    $this->db->where("ID", $id);

    $query = $this->db->get();
    return $query->row_array();

  }

  public function getJudul($norm){
    // $query = "SELECT NORM, getNamaLengkap(NORM) AS NAMAPASIEN 
    //       FROM master.pasien 
    //       WHERE NORM = ?";
    // $result = $this->db->query($query, array($norm));
    // return $result;

    $query = $this->db
    ->select('NORM, getNamaLengkap(NORM) NAMAPASIEN')
    ->from('master.pasien')
    ->where('NORM', $norm)
    ->get();
    return $query;
  }
  public function daataRadiologi($nopen){
    $this->db->select('hrad.TINDAKAN_MEDIS ID, mt.NAMA NAMATINDAKAN,kun.MASUK TANGGAL_KUNJUNGAN');
		$this->db->from('pendaftaran.pendaftaran pp');
    $this->db->join('pendaftaran.kunjungan kun', 'kun.NOPEN = pp.NOMOR', 'left');
    $this->db->join('layanan.tindakan_medis tm', 'tm.KUNJUNGAN = kun.NOMOR', 'left');
    $this->db->join('layanan.hasil_rad hrad', 'hrad.TINDAKAN_MEDIS = tm.ID', 'left');
    $this->db->join('master.tindakan mt', 'mt.ID= tm.TINDAKAN', 'left');
    // $this->db->join('master.dokter dok1 ','dok1.ID = hrad.DOKTER_SATU' , 'left');
    // $this->db->join('master.dokter dok2 ','dok2.ID = hrad.DOKTER_DUA', 'left');
    $this->db->where('pp.NOMOR', $nopen);
    $this->db->where('kun.RUANGAN', 105100101);
    $this->db->where("YEAR(kun.MASUK)", date('Y'));
    $this->db->where('pp.STATUS !=', 0); 
    $this->db->where('hrad.STATUS !=', 0);
    $this->db->order_by('hrad.TINDAKAN_MEDIS', 'desc');
    $this->db->order_by('kun.MASUK', 'desc');


    $query = $this->db->get();
    return $query;
  }
  public function hasilRadiologi($id){
    $this->db->select('pp.NORM,hrad.TANGGAL TGLHASIL, hrad.KESAN,hrad.HASIL,hrad.STATUS,
    master.getNamaLengkapPegawai(dok1.NIP) DOKTER_SATU, 
    master.getNamaLengkapPegawai(dok2.NIP) DOKTER_DUA');
    $this->db->from('layanan.hasil_rad hrad');
    // $this->db->join('master.tindakan mt', 'mt.ID= tm.TINDAKAN', 'left');
    $this->db->join('master.dokter dok1 ','dok1.ID = hrad.DOKTER_SATU' , 'left');
    $this->db->join('master.dokter dok2 ','dok2.ID = hrad.DOKTER_DUA', 'left');
    $this->db->join('layanan.tindakan_medis tm', 'tm.ID =hrad.TINDAKAN_MEDIS ', 'left');
    $this->db->join('pendaftaran.kunjungan kun', 'kun.NOMOR= tm.KUNJUNGAN', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = kun.NOPEN', 'left');
    $this->db->where('hrad.TINDAKAN_MEDIS', $id);
    $this->db->order_by('hrad.TINDAKAN_MEDIS', 'desc');
    $query = $this->db->get();
    return $query;
  }
  //HASIL LAB PA
  //sito
  function datatablesSito($nopen){
    $this->sitologi($nopen);
    if($_POST["length"] != -1){
    $this->db->limit($_POST["length"], $_POST["start"]);
    }
    $query = $this->db->get();
    return $query->result();
}

function filter_count_Sito($nopen){
    $this->sitologi($nopen);
    $query = $this->db->get();
    return $query->num_rows();
}

function total_count_Sito($nopen){
    $this->sitologi($nopen);
    return $this->db->count_all_results();
}
public function sitologi($nopen)
	{
		$this->db->select('hps.ID, hps.NOMOR_LAB,lol.TANGGAL TANGGAL_ORDER,hps.TANGGAL_LAB,hps.LOKASI');
		$this->db->from('layanan.hasil_pa_sitologi hps');
		$this->db->join('master.dokter md', 'hps.DOKTER = md.ID', 'left');
		$this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hps.KUNJUNGAN', 'left');
    // $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = kun1.NOPEN', 'left');
		$this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR', 'left');
		$this->db->join('master.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
		$this->db->where('kun1.NOPEN', $nopen);
        $this->db->where('hps.STATUS', 2);

        $this->db->group_start();
        $this->db->like('hps.NOMOR_LAB', $_POST['search']['value']);
        $this->db->or_like("lol.TANGGAL", $_POST['search']['value']);
        $this->db->or_like("hps.TANGGAL_LAB", $_POST['search']['value']);
        $this->db->group_end();
	}
  public function getSito($id){
    $this->db->select('
      hps.ID,
      hps.NOMOR_LAB,
      hps.LOKASI,
      hps.DIDAPAT_DENGAN,
      hps.CAIRAN_FIKSASI,
      hps.DIAGNOSA_KLINIK,
      hps.KETERANGAN_KLINIK,
      hps.MAKROSKOPIK,
      hps.MIKROSKOPIK,
      hps.KESIMPULAN,
      hps.IMUNO_HISTOKIMIA,
      hps.REEVALUASI,
      hps.TANGGAL_LAB,
      hps.TANGGAL,
      master.getNamaLengkapPegawai(md.NIP) AS PEMERIKSA,
      master.getNamaLengkapPegawai(mdp.NIP) AS PENGIRIM,
      kun1.MASUK AS TERIMA,
      mru.DESKRIPSI AS RUANGAN,
      kun1.NOMOR AS TIND,
      maref.DESKRIPSI AS PENJAMIN
    ');
    $this->db->from('layanan.hasil_pa_sitologi hps');
    $this->db->join('master.dokter md', 'hps.DOKTER = md.ID', 'left');
    $this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hps.KUNJUNGAN', 'left');
    $this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR', 'left');
    $this->db->join('master.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
    $this->db->join('pendaftaran.kunjungan kun2', 'kun2.NOMOR = lol.KUNJUNGAN', 'left');
    $this->db->join('master.ruangan mru', 'kun2.RUANGAN = mru.ID', 'left');
    $this->db->join('pendaftaran.penjamin ppj', 'kun1.NOPEN = ppj.NOPEN', 'left');
    $this->db->join('master.referensi maref', 'ppj.JENIS = maref.ID AND maref.JENIS = 10', 'left');
    $this->db->where('kun2.NOPEN', $nopen);
    $this->db->group_start();
    $this->db->where('hps.TANGGAL_LAB >', '2018-08-23 00:00:00');
    $this->db->where_in('hps.STATUS', [1, 2]);
    $this->db->group_end();

    $query = $this->db->get();
    return $query->row_array();

  }

//histo
public function histologi($nopen)
{
$this->db->select('hph.ID, hph.NOMOR_LAB,lol.TANGGAL TANGGAL_ORDER, hph.TANGGAL_LAB, hph.LOKASI 
  , master.getNamaLengkapPegawai(md.NIP) PEMERIKSA, master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM ');
$this->db->from('layanan.hasil_pa_histologi hph ');
$this->db->join('master.dokter md', 'hph.DOKTER = md.ID ', 'left');
$this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hph.KUNJUNGAN', 'left');
$this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR ', 'left');
$this->db->join('master.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
$this->db->where('kun1.NOPEN', $nopen);
$this->db->where('hph.STATUS', 2);
$this->db->order_by('hph.ID', 'desc');

$this->db->group_start();
    $this->db->like('hph.NOMOR_LAB', $_POST['search']['value']);
    $this->db->or_like("lol.TANGGAL", $_POST['search']['value']);
    $this->db->or_like("hph.TANGGAL_LAB", $_POST['search']['value']);
    $this->db->group_end();
}

function datatablesHisto($nopen){
    $this->histologi($nopen);
    if($_POST["length"] != -1){
    $this->db->limit($_POST["length"], $_POST["start"]);
    }
    $query = $this->db->get();
    return $query->result();
}

function filter_count_Histo($nopen){
    $this->histologi($nopen);
    $query = $this->db->get();
    return $query->num_rows();
}

function total_count_Histo($nopen){
    $this->histologi($nopen);
    return $this->db->count_all_results();
}
public function getHisto($nolab){
    $this->db->select('
    hph.ID,
    hph.NOMOR_LAB,
    hph.LOKASI,
    hph.DIDAPAT_DENGAN,
    hph.CAIRAN_FIKSASI,
    hph.DIAGNOSA_KLINIK,
    hph.KETERANGAN_KLINIK,
    hph.MAKROSKOPIK,
    hph.MIKROSKOPIK,
    hph.KESIMPULAN,
    hph.IMUNO_HISTOKIMIA,
    hph.REEVALUASI,
    hph.TANGGAL_LAB,
    hph.TANGGAL,
    master.getNamaLengkapPegawai(md.NIP) AS PEMERIKSA,
    master.getNamaLengkapPegawai(mdp.NIP) AS PENGIRIM,
    kun1.MASUK AS TERIMA,
    mru.DESKRIPSI AS RUANGAN,
    kun1.NOMOR AS TIND,
    maref.DESKRIPSI AS PENJAMIN
  ');
  $this->db->from('layanan.hasil_pa_histologi hph');
  $this->db->join('master.dokter md', 'hph.DOKTER = md.ID', 'left');
  $this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hph.KUNJUNGAN', 'left');
  $this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR', 'left');
  $this->db->join('master.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
  $this->db->join('pendaftaran.kunjungan kun2', 'kun2.NOMOR = lol.KUNJUNGAN', 'left');
  $this->db->join('master.ruangan mru', 'kun2.RUANGAN = mru.ID', 'left');
  $this->db->join('pendaftaran.penjamin ppj', 'kun1.NOPEN = ppj.NOPEN', 'left');
  $this->db->join('master.referensi maref', 'ppj.JENIS = maref.ID AND maref.JENIS = 10', 'left');
  $this->db->where('hph.NOMOR_LAB', $nolab);
  $this->db->group_start();
  $this->db->where('hph.TANGGAL_LAB >', '2018-08-23 00:00:00');
  $this->db->where_in('hph.STATUS', [1, 2]);
  $this->db->group_end();
  $query = $this->db->get();
  return $query->row_array();

}
    

//imuno
public function imuno($nopen)
{
$this->db->select('hph.ID, hph.NOMOR_LAB NOLABSBLMNYA,hph.NOMOR_IMUNO,lol.TANGGAL TANGGAL_ORDER, hph.TANGGAL_IMUNO TANGGAL_LAB, master.getNamaLengkapPegawai(md.NIP) PEMERIKSA, master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM');
$this->db->from('layanan.hasil_pa_imunohistokimia hph');
$this->db->join('master.dokter md', 'hph.DOKTER = md.ID ', 'left');
$this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hph.KUNJUNGAN', 'left');
$this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR ', 'left');
$this->db->join('master.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
$this->db->where('kun1.NOPEN', $nopen);;
    $this->db->where('hph.STATUS', 2);
$this->db->order_by('hph.ID', 'desc');

$this->db->group_start();
    $this->db->like('hph.NOMOR_IMUNO', $_POST['search']['value']);
    $this->db->or_like("lol.TANGGAL", $_POST['search']['value']);
    $this->db->or_like("hph.TANGGAL_IMUNO", $_POST['search']['value']);
    $this->db->group_end();
}

function datatablesImuno($nopen){
    $this->imuno($nopen);
    if($_POST["length"] != -1){
    $this->db->limit($_POST["length"], $_POST["start"]);
    }
    $query = $this->db->get();
    return $query->result();
}

function filter_count_Imuno($nopen){
    $this->imuno($nopen);
    $query = $this->db->get();
    return $query->num_rows();
}

function total_count_Imuno($nopen){
    $this->imuno($nopen);
    return $this->db->count_all_results();
}
public function getImuno($id){
  $this->db->select('
    pahis.NOMOR_LAB AS NOLAB_SBLMNYA,
    hph.NOMOR_IMUNO AS NOMOR_LAB,
    pahis.LOKASI,
    pahis.DIDAPAT_DENGAN,
    pahis.CAIRAN_FIKSASI,
    pahis.DIAGNOSA_KLINIK,
    pahis.KETERANGAN_KLINIK,
    pahis.KESIMPULAN AS KESIMPULAN_LAB_SBLMYA,
    hph.ID,
    hph.IMUNO_HISTOKIMIA,
    hph.KESIMPULAN,
    hph.TANGGAL_HASIL AS TANGGAL,
    master.getNamaLengkapPegawai(md.NIP) AS PEMERIKSA,
    master.getNamaLengkapPegawai(mdp.NIP) AS PENGIRIM,
    kun1.MASUK AS TERIMA,
    mru.DESKRIPSI AS RUANGAN,
    kun1.NOMOR AS TIND,
    maref.DESKRIPSI AS PENJAMIN
  ');
  $this->db->from('layanan.hasil_pa_imunohistokimia hph');
  $this->db->join('master.dokter md', 'hph.DOKTER = md.ID', 'left');
  $this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hph.KUNJUNGAN', 'left');
  $this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR', 'left');
  $this->db->join('master.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
  $this->db->join('layanan.hasil_pa_histologi pahis', 'pahis.NOMOR_LAB = hph.NOMOR_LAB', 'left');
  $this->db->join('pendaftaran.kunjungan kun2', 'kun2.NOMOR = lol.KUNJUNGAN', 'left');
  $this->db->join('master.ruangan mru', 'kun2.RUANGAN = mru.ID', 'left');
  $this->db->join('pendaftaran.penjamin ppj', 'kun1.NOPEN = ppj.NOPEN', 'left');
  $this->db->join('master.referensi maref', 'ppj.JENIS = maref.ID AND maref.JENIS = 10', 'left');
  $this->db->where('hph.JENIS_HASIL', 1);
  $this->db->where_in('hph.STATUS', [1, 2]);
  $this->db->where('hph.NOMOR_IMUNO', $id);

  $query = $this->db->get();
  return $query->row_array();

}
   

//patmol
public function patmol($nopen)
{
  $this->db->select("pt.*
      , IF(pt.JENIS_PEMERIKSAAN=1, 'EGFR'
      , IF(pt.JENIS_PEMERIKSAAN=2, 'HPV-GENOTYPING'
      , IF(pt.JENIS_PEMERIKSAAN=3, 'HPV Hybrid Captured'
      , IF(pt.JENIS_PEMERIKSAAN=4, 'BRAF'
      , IF(pt.JENIS_PEMERIKSAAN=5, 'KRAS'
      , IF(pt.JENIS_PEMERIKSAAN=6, 'ALL RAS'
      , IF(pt.JENIS_PEMERIKSAAN=7, 'IDH 1/2'
      , IF(pt.JENIS_PEMERIKSAAN=8, 'CYTO DNA'
      , IF(pt.JENIS_PEMERIKSAAN=9, 'DISH', '-'))))))))) DESK_JENIS_PEMERIKSAAN
      , lol.TANGGAL TANGGAL_ORDER");
  $this->db->from('layanan.hasil_pa_patmol pt');
  $this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = pt.KUNJUNGAN', 'left');
  $this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR ', 'left');
  $this->db->where('pt.STATUS', 2);
  $this->db->where('kun1.NOPEN', $nopen);;
  // $this->db->where("pt.NORM=$norm OR pt.MR_INTERNAL=$norm");

  $this->db->group_start();
      $this->db->like("IF(pt.JENIS_PEMERIKSAAN=1, 'EGFR'
      , IF(pt.JENIS_PEMERIKSAAN=2, 'HPV-GENOTYPING'
      , IF(pt.JENIS_PEMERIKSAAN=3, 'HPV Hybrid Captured'
      , IF(pt.JENIS_PEMERIKSAAN=4, 'BRAF'
      , IF(pt.JENIS_PEMERIKSAAN=5, 'KRAS'
      , IF(pt.JENIS_PEMERIKSAAN=6, 'ALL RAS'
      , IF(pt.JENIS_PEMERIKSAAN=7, 'IDH 1/2'
      , IF(pt.JENIS_PEMERIKSAAN=8, 'CYTO DNA'
      , IF(pt.JENIS_PEMERIKSAAN=9, 'DISH', '-')))))))))", $_POST['search']['value']);
  $this->db->or_like("DATE_FORMAT(lol.TANGGAL,'%d-%m-%Y %H:%i:%s')", $_POST['search']['value']);
  // $this->db->or_like("hph.TANGGAL_IMUNO", $_POST['search']['value']);
  $this->db->group_end();
}

function datatablesPatmol($nopen){
    $this->patmol($nopen);
    if($_POST["length"] != -1){
    $this->db->limit($_POST["length"], $_POST["start"]);
    }
    $query = $this->db->get();
    return $query->result();
}

function filter_count_Patmol($nopen){
    $this->patmol($nopen);
    $query = $this->db->get();
    return $query->num_rows();
}

function total_count_Patmol($nopen){
    $this->patmol($nopen);
    return $this->db->count_all_results();
}
public function getPatmol($id)
{
  $this->db->select('
    hph.ID,
    hph.NOMOR_LAB,
    master.getNamaLengkapPegawai(md.NIP) AS PEMERIKSA,
    master.getNamaLengkapPegawai(mdp.NIP) AS PENGIRIM,
    kun1.MASUK AS TERIMA,
    mru.DESKRIPSI AS RUANGAN,
    kun1.NOMOR AS TIND,
    maref.DESKRIPSI AS PENJAMIN
  ');
  $this->db->from('layanan.hasil_pa_patmol hph');
  $this->db->join('master.dokter md', 'hph.DOKTER = md.ID', 'left');
  $this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hph.KUNJUNGAN', 'left');
  $this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR', 'left');
  $this->db->join('master.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
  $this->db->join('master.icd_o_morphology mio', 'hph.ICDOMORPHOLOGY = mio.ID', 'left');
  $this->db->join('master.icd_o_topography mit', 'hph.ICDOTOPOGRAPHY = mit.ID', 'left');
  $this->db->join('pendaftaran.kunjungan kun2', 'kun2.NOMOR = lol.KUNJUNGAN', 'left');
  $this->db->join('master.ruangan mru', 'kun2.RUANGAN = mru.ID', 'left');
  $this->db->join('pendaftaran.penjamin ppj', 'kun1.NOPEN = ppj.NOPEN', 'left');
  $this->db->join('master.referensi maref', 'ppj.JENIS = maref.ID AND maref.JENIS = 10', 'left');
  $this->db->where('hph.NOMOR_LAB', $id);
  $this->db->group_start();
  $this->db->where('hph.TANGGAL_LAB >', '2018-08-23 00:00:00');
  $this->db->where_in('hph.STATUS', [1, 2]);
  $this->db->group_end();

  $query = $this->db->get();
  return $query->row_array();

}




//sampai sini

public function hasil_patmol($nolab)
{
$query = $this->db->query(
  "SELECT pt.*
  , pt.ID
  , pt.KUNJUNGAN
  , IF(pt.MR_INTERNAL=0, pt.NORM, pt.MR_INTERNAL) NORM
  , pt.NOMOR_PATMOL NOMOR_LAB
  -- , IF(pt.JENIS_PEMERIKSAAN=1, 'EGFR'
  -- , IF(pt.JENIS_PEMERIKSAAN=2, 'HPV-GENOTYPING'
  -- , IF(pt.JENIS_PEMERIKSAAN=3, 'HPV Hybrid Captured'
  -- , IF(pt.JENIS_PEMERIKSAAN=4, 'BRAF'
  -- , IF(pt.JENIS_PEMERIKSAAN=5, 'KRAS'
  -- , IF(pt.JENIS_PEMERIKSAAN=6, 'ALL RAS'
  -- , IF(pt.JENIS_PEMERIKSAAN=7, 'IDH 1/2'
  -- , IF(pt.JENIS_PEMERIKSAAN=8, 'CYTO DNA'
  -- , IF(pt.JENIS_PEMERIKSAAN=9, 'DISH', '-'))))))))) JENIS_PEMERIKSAAN
  , master.getNamaLengkapPegawai(d.NIP) PEMERIKSA
  , DATE_FORMAT(pt.TANGGAL_LAB,'%d-%m-%Y %H:%i:%s') TANGGAL_PATMOL_FORMAT
  , DATE_FORMAT(pt.TANGGAL_HASIL,'%d-%m-%Y %H:%i:%s') TANGGAL_HASIL_FORMAT
  , master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM
  , DATE_FORMAT(kun1.MASUK,'%d-%m-%Y %H:%i:%s') TERIMA
  , mru.DESKRIPSI RUANGAN, kun1.NOMOR TIND, maref.DESKRIPSI PENJAMIN
 
  FROM layanan.hasil_pa_patmol pt
  LEFT JOIN master.dokter d ON pt.DOKTER = d.ID
 
  LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = pt.KUNJUNGAN
  LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR
  LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID

  LEFT JOIN pendaftaran.kunjungan kun2 ON kun2.NOMOR = lol.KUNJUNGAN
  LEFT JOIN master.ruangan mru ON kun2.RUANGAN = mru.ID
  LEFT JOIN pendaftaran.penjamin ppj ON kun1.NOPEN = ppj.NOPEN
  LEFT JOIN master.referensi maref ON ppj.JENIS = maref.ID AND maref.JENIS = 10      
 
  WHERE pt.NOMOR_PATMOL='$nolab'"
);
return $query->row_array();
}

function getIdrg($param){
        if ($this->input->get('q')) {
            $this->db->like('dx.CODE', $this->input->get('q'));
        }
        $this->db->select("dx.CODE ICD, dx.DESCRIPTION ICD_DESKRIPSI, dx.VALIDCODE , dx.ACCPDX ACCPDX");
        $this->db->from('medicalrecord.code_idrg dx');
        $this->db->where("dx.SYSTEM LIKE '$param'");
        $this->db->order_by('dx.CODE', 'ASC');
        $this->db->limit(10);
        
        $query = $this->db->get();
        return $query->result_array();
    }

public function viewKlaimBPJS($nopen)
{
$query = $this->db->query(
  "SELECT pp.NOMOR NOPEN, ps.NORM, master.getNamaLengkap(ps.NORM) NAMA_PASIEN
		,  CASE WHEN ps.TEMPAT_LAHIR = w.ID THEN w.DESKRIPSI
				ELSE ps.TEMPAT_LAHIR	END AS TEMPAT_LAHIR
		, date_format(ps.TANGGAL_LAHIR,'%d %M %Y')TGL_LAHIR
		, master.getCariUmur((pp.TANGGAL),ps.TANGGAL_LAHIR) UMUR
		, jk.DESKRIPSI JENIS_KELAMIN
		, sts.DESKRIPSI STATUS_PASIEN
		, mr.DESKRIPSI RUANG_DAFTAR
		, master.getNamaLengkapPegawai(md.NIP) DPJP
		, jmn.DESKRIPSI JENIS_JAMINAN
		, pjm.NOMOR SEP, kls.DESKRIPSI KELAS_BPJS, kap.NOMOR NO_BPJS
	
FROM pendaftaran.pendaftaran pp
LEFT JOIN master.pasien ps ON ps.NORM = pp.NORM
LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pp.NOMOR
LEFT JOIN pendaftaran.penjamin pjm ON pjm.NOPEN = pp.NOMOR
LEFT JOIN master.kartu_asuransi_pasien kap ON kap.NORM = ps.NORM AND kap.JENIS=2
LEFT JOIN master.wilayah w ON w.ID = ps.TEMPAT_LAHIR
LEFT JOIN master.referensi sts ON sts.ID = ps.STATUS AND sts.JENIs=13
LEFT JOIN master.referensi jk ON jk.ID = ps.JENIS_KELAMIN AND jk.JENIS=2
LEFT JOIN master.dokter md ON md.ID = tp.DOKTER
LEFT JOIN master.ruangan mr ON mr.ID = tp.RUANGAN
LEFT JOIN master.referensi kls ON kls.ID = pjm.KELAS AND kls.JENIS =19
LEFT JOIN master.referensi jmn ON jmn.ID = pjm.JENIS AND jmn.JENIS =10

WHERE pp.NOMOR = '$nopen'"
);
return $query->row_array();
}

public function hasil_sito($nolab)
{
$this->db->select('hps.*
  ,master.getNamaLengkapPegawai(md.NIP) PEMERIKSA, master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM, kun1.MASUK TERIMA
  , mru.DESKRIPSI RUANGAN, kun1.NOMOR TIND, maref.DESKRIPSI PENJAMIN');
$this->db->from('layanan.hasil_pa_sitologi hps');
$this->db->join('`master`.dokter md', 'hps.DOKTER = md.ID', 'left');
$this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hps.KUNJUNGAN', 'left');
$this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR', 'left');
$this->db->join('`master`.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
$this->db->join('pendaftaran.kunjungan kun2', 'kun2.NOMOR = lol.KUNJUNGAN', 'left');
$this->db->join('`master`.ruangan mru', 'kun2.RUANGAN = mru.ID', 'left');
$this->db->join('pendaftaran.penjamin ppj', 'kun1.NOPEN = ppj.NOPEN', 'left');
$this->db->join('`master`.referensi maref', 'ppj.JENIS = maref.ID AND maref.JENIS = 10', 'left');
$this->db->where('IF(hps.TANGGAL_LAB > "2018-08-23 00:00:00", hps.`STATUS`=2, hps.`STATUS` in (1,2))');

$this->db->where('hps.NOMOR_LAB', $nolab);

$query	= $this->db->get();
return $query->row_array();
}

public function hasil_histo($nolab)
{
$this->db->select("hph.* ,master.getNamaLengkapPegawai(md.NIP) PEMERIKSA
  , master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM, kun1.MASUK TERIMA
  , CONCAT(mio.KODE,'-',mio.DESKRIPSI) ICDOMOR , CONCAT(mit.KODE,'-',mit.DESKRIPSI) ICDOMT
  , mru.DESKRIPSI RUANGAN, kun1.NOMOR TIND, maref.DESKRIPSI PENJAMIN");
$this->db->from('layanan.hasil_pa_histologi hph');
$this->db->join('`master`.dokter md', 'hph.DOKTER = md.ID', 'left');
$this->db->join('pendaftaran.kunjungan kun1', 'kun1.NOMOR = hph.KUNJUNGAN ', 'left');
$this->db->join('layanan.order_lab lol', 'kun1.REF = lol.NOMOR ', 'left');
$this->db->join('`master`.dokter mdp', 'lol.DOKTER_ASAL = mdp.ID', 'left');
$this->db->join('`master`.icd_o_morphology mio', 'hph.ICDOMORPHOLOGY = mio.ID ', 'left');
$this->db->join('`master`.icd_o_topography mit', 'hph.ICDOTOPOGRAPHY = mit.ID ', 'left');
$this->db->join('pendaftaran.kunjungan kun2', 'kun2.NOMOR = lol.KUNJUNGAN ', 'left');
$this->db->join('master.ruangan mru', 'kun2.RUANGAN = mru.ID ', 'left');
$this->db->join('pendaftaran.penjamin ppj', 'kun1.NOPEN = ppj.NOPEN ', 'left');
$this->db->join('`master`.referensi maref', 'ppj.JENIS = maref.ID AND maref.JENIS = 10', 'left');
$this->db->where('IF(hph.TANGGAL_LAB > "2018-08-23 00:00:00", hph.`STATUS`=2, hph.`STATUS` in (1,2))');

$this->db->where('hph.NOMOR_LAB', $nolab);

$query	= $this->db->get();
return $query->row_array();
}

public function hasil_imunohistokimia($nolab)
{
  $query = $this->db->query("SELECT IF(hph.JENIS_HASIL=1, pahis.NOMOR_LAB, pasit.NOMOR_LAB) NOLAB_SBLMNYA, hph.NOMOR_IMUNO NOMOR_LAB

    , IF(hph.JENIS_HASIL=1, pahis.LOKASI, pasit.LOKASI) LOKASI

    , IF(hph.JENIS_HASIL=1, pahis.DIDAPAT_DENGAN, pasit.DIDAPAT_DENGAN) DIDAPAT_DENGAN

    , IF(hph.JENIS_HASIL=1, pahis.CAIRAN_FIKSASI, pasit.CAIRAN_FIKSASI) CAIRAN_FIKSASI

    , IF(hph.JENIS_HASIL=1, pahis.DIAGNOSA_KLINIK, pasit.DIAGNOSA_KLINIK) DIAGNOSA_KLINIK

    , IF(hph.JENIS_HASIL=1, pahis.KETERANGAN_KLINIK, pasit.KETERANGAN_KLINIK) KETERANGAN_KLINIK

    , IF(hph.JENIS_HASIL=1, pahis.KESIMPULAN, pasit.KESIMPULAN) KESIMPULAN_LAB_SBLMYA

    , hph.ID, hph.KUNJUNGAN, hph.NORM, hph.JENIS_HASIL

    , hph.IMUNO_HISTOKIMIA, hph.KESIMPULAN, hph.DOKTER, hph.TANGGAL_IMUNO TANGGAL_LAB, hph.TANGGAL_HASIL TANGGAL

    , hph.OLEH, hph.`STATUS`

    , master.getNamaLengkapPegawai(md.NIP) PEMERIKSA

    , master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM, kun1.MASUK TERIMA

    #, CONCAT(mio.KODE,'-',mio.DESKRIPSI) ICDOMOR , CONCAT(mit.KODE,'-',mit.DESKRIPSI) ICDOMT

    , mru.DESKRIPSI RUANGAN, kun1.NOMOR TIND, maref.DESKRIPSI PENJAMIN

  FROM layanan.hasil_pa_imunohistokimia hph

  LEFT JOIN master.dokter md ON hph.DOKTER = md.ID

  LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = hph.KUNJUNGAN 

  LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR 

  LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID



  LEFT JOIN layanan.hasil_pa_histologi pahis ON pahis.NOMOR_LAB = hph.NOMOR_LAB

  LEFT JOIN layanan.hasil_pa_sitologi pasit ON pasit.NOMOR_LAB = hph.NOMOR_LAB

  #LEFT JOIN master.icd_o_morphology mio ON hph.ICDOMORPHOLOGY = mio.ID 

  #LEFT JOIN master.icd_o_topography mit ON hph.ICDOTOPOGRAPHY = mit.ID 

  LEFT JOIN pendaftaran.kunjungan kun2 ON kun2.NOMOR = lol.KUNJUNGAN 

  LEFT JOIN master.ruangan mru ON kun2.RUANGAN = mru.ID 

  LEFT JOIN pendaftaran.penjamin ppj ON kun1.NOPEN = ppj.NOPEN 

  LEFT JOIN master.referensi maref ON ppj.JENIS = maref.ID AND maref.JENIS = 10

  WHERE hph.`STATUS`=2

    and hph.NOMOR_IMUNO='$nolab'");
  return $query->row_array();
}
  //END LAB PA

public function dataListcosting_user($userid, $start, $length, $search_value, $tanggal = null)
{
    
    $this->db->select("COUNT(*) as total");
    $this->db->from('db_rekammedis.tb_coding_detail det');
    $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
    $this->db->where('det.ID_PETUGAS', $userid);     
    $this->db->group_start();       
    $this->db->where('det.STATUS', 1); 
    $this->db->or_where('det.STATUS', 3);
    $this->db->or_group_start();
    $this->db->where('det.STATUS', 2);
    $this->db->where('cod.TANGGAL_TARIK', date('Y-m-d'));
    $this->db->group_end();
    $this->db->group_end();

    
    $query_total = $this->db->get()->row();
    $recordsTotal = $query_total->total;// UNTUK MENENTUKAN JUMLAHNYA
    if (!empty($tanggal)) {
      $this->db->where('DATE(det.TANGGAL_LAYANAN)', $tanggal);
    }

    $this->db->select("
        dr.DESKRIPSI AS statusSEP,
        DATE_FORMAT(det.DONE_DATE, '%d-%m-%Y') AS DONE_DATE,
        det.ID,
        cod.TANGGAL_TARIK,
        det.TANGGAL_LAYANAN,
        kod.NAMA AS COSTING,
        det.NORM,
        det.NAMA_PASIEN,
        det.NOPEN,
        mr.DESKRIPSI,
        det.TANGGAL_PENDAFTARAN,
        det.SEP,
        det.HASIL_RAD,
        det.STATUS_COSTING,
        det.ID_PETUGAS,
        top2.nama_lengkap AS namaPetugas,
        det.STATUS
    ");
    $this->db->from('db_rekammedis.tb_coding_detail det');
    $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
    $this->db->join('db_rekammedis.tb_operator top2', 'top2.ID = det.ID_PETUGAS', 'left');
    $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
    $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
    $this->db->join('db_rekammedis.koder kod', 'kod.ID = det.DONE_BY', 'left');
    $this->db->where('det.ID_PETUGAS', $userid); 
    $this->db->group_start();       
    $this->db->where('det.STATUS', 1); 
    $this->db->or_where('det.STATUS', 3);
    $this->db->or_group_start();
    $this->db->where('det.STATUS', 2);
    $this->db->where('cod.TANGGAL_TARIK', date('Y-m-d'));
    $this->db->group_end();
    $this->db->group_end();
    // $this->db->order_by('IFNULL(det.DONE_DATE, "1970-01-01 00:00:00")');
    // $this->db->order_by('det.DONE_DATE', 'ASC');
    // $this->db->order_by('det.STATUS_COSTING', 'DESC');
    $this->db->order_by("FIELD(det.STATUS_COSTING, 'COSTING', 'BELUM COSTING', 'BATAL COSTING')", '');
    $this->db->order_by('det.TANGGAL_LAYANAN', 'ASC');

    

    // Pencarian jika ada nilai search
    if (!empty($search_value)) {
        $this->db->group_start();
        $this->db->like("det.NAMA_PASIEN", $search_value);
        $this->db->or_like("det.NORM", $search_value);
        $this->db->or_like("det.SEP", $search_value);
        $this->db->group_end();
    }

    
    $filtered_query = clone $this->db;
    $this->db->limit($length, $start);
    $query = $this->db->get();

    
    $filtered_result = $filtered_query->get()->num_rows();

    
    return [
        'data' => $query, 
        'recordsTotal' => $recordsTotal,
        'recordsFiltered' => $filtered_result
    ];
}

  
  

public function editlistCosting($id)
  {
    $query = $this->db->query("
      SELECT det.*, has.DESKRIPSI
      FROM db_rekammedis.tb_coding_detail det
      LEFT JOIN db_rekammedis.tb_master_hasil has ON has.ID = det.HASIL_RAD
      WHERE det.ID = $id");
    return $query;
  }

function list_Hasil(){
        if ($this->input->get('q')) {
            $this->db->like('has.DESKRIPSI ', $this->input->get('q'));
        }
        $this->db->select("*");
        $this->db->from('db_rekammedis.tb_master_hasil has');
        $this->db->where("has.STATUS",1);
        $this->db->order_by('has.ID', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }

  public  function datatablesCppt($nopen){
    $this->db->select(
      'ktcp.id IDCPPT,ktcp.*, master.getNamaLengkap(pp.NORM) NAMAPASIEN,
      master.getNamaLengkapPegawai(ap.NIP)NAMAPEGAWAI, pro.DESKRIPSI PROFESI, mru.ID IDRUANGAN,
      mru.DESKRIPSI RUANGAN, mru.JENIS_KUNJUNGAN, master.getNamaLengkapPegawai(md.NIP) DOKTERDPJP,
      master.getNamaLengkapPegawai(mdtbak.NIP) DOKTERTBAK, ktcp.jenis_cppt JENIS_CPPT,
      master.getNamaLengkapPegawai(apv.NIP) VERIFOLEH, HOUR(TIMEDIFF(NOW(),ktcp.tanggal)) DURASI_CPPT,
      IF(HOUR(TIMEDIFF(NOW(),ktcp.tanggal)) <= 24,1,0) STATUS_EDIT_CPPT,
      (SELECT IF(COUNT(*) > 0, 1, 0) FROM keperawatan.tb_tbak_detail td
      WHERE td.id_cppt = ktcp.id AND td.`status` != 0) TBAK'
    );
    $this->db->from('keperawatan.tb_cppt ktcp');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ktcp.nokun', 'LEFT');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'LEFT');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = ktcp.oleh', 'LEFT');
    $this->db->join('master.pegawai peg', 'peg.NIP = ap.NIP', 'LEFT');
    $this->db->join('master.referensi pro', 'pro.ID = peg.PROFESI AND pro.JENIS = 36', 'LEFT');
    $this->db->join('master.ruangan mru', 'mru.ID = pk.RUANGAN', 'LEFT');
    $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pk.NOPEN', 'LEFT');
    $this->db->join('master.dokter md', 'md.ID = ptp.DOKTER', 'LEFT');
    $this->db->join('master.dokter mdtbak', 'mdtbak.ID = ktcp.dokter_tbak', 'LEFT');
    $this->db->join('aplikasi.pengguna apv', 'apv.ID = ktcp.verif_oleh', 'LEFT');

    $this->db->where('ktcp.STATUS !=', '0');
    $this->db->where('pp.NOMOR', $nopen); 
    $this->db->order_by('ktcp.id', 'DESC');
    
       
    $query = $this->db->get();
    return $query->result(); 
  }
  public function datatablesTagihan($nopen)
  {
    $this->db->select('tpd.TAGIHAN, t.TANGGAL, t.TOTAL, IF(pt.TANGGAL IS NULL, SYSDATE(), pt.TANGGAL) AS TANGGALBAYAR');
    $this->db->from('pembayaran.tagihan t');
    $this->db->join('pembayaran.pembayaran_tagihan pt', 'pt.TAGIHAN = t.ID AND pt.JENIS = 1 AND pt.STATUS = 1', 'left');
    $this->db->join('pembayaran.tagihan_pendaftaran tpd', 't.ID = tpd.TAGIHAN AND tpd.STATUS = 1 AND tpd.UTAMA = 1', 'left');
    $this->db->where('tpd.PENDAFTARAN', $nopen);

    $query = $this->db->get();
    return $query->result();
  }


  //HISTORY
  // public function getHistory($userid, $start, $length, $searchValue = null)
  //   {
  //     $this->db->select("
  //     dr.DESKRIPSI AS statusSEP,
  //     DATE_FORMAT(det.DONE_DATE, '%d-%m-%Y') AS DONE_DATE,
  //     det.ID,
  //     cod.TANGGAL_TARIK,
  //     det.TANGGAL_LAYANAN,
  //     kod.NAMA AS COSTING,
  //     det.NORM,
  //     det.NAMA_PASIEN,
  //     det.NOPEN,
  //     mr.DESKRIPSI,
  //     det.TANGGAL_PENDAFTARAN,
  //     det.SEP,
  //     det.HASIL_RAD,
  //     det.STATUS
  //     ");
  //     $this->db->from('db_rekammedis.tb_coding_detail det');
  //     $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
  //     $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
  //     $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
  //     $this->db->join('db_rekammedis.koder kod', 'kod.ID = det.DONE_BY', 'left');
  //     $this->db->where('cod.ID_PETUGAS', $userid);
  //     $this->db->where('det.STATUS !=', 1);
  //     if ($searchValue) {
  //         $this->db->like('det.NORM', $searchValue);
  //     }

  //       if ($this->input->post('norm')) {
  //           $this->db->where('det.NORM', $this->input->post('norm'));
  //       }
  //       if ($this->input->post('awalLayanan')) {
  //           $this->db->where('det.TANGGAL_LAYANAN >=', $this->input->post('awalLayanan'));
  //       }
  //       if ($this->input->post('akhirLayanan')) {
  //           $this->db->where('det.TANGGAL_LAYANAN <=', $this->input->post('akhirLayanan'));
  //       }
  //       if ($this->input->post('awalGrouping')) {
  //         $this->db->where('det.DONE_DATE >=', $this->input->post('awalGrouping'));
  //     }
  //     if ($this->input->post('akhirGrouping')) {
  //         $this->db->where('det.DONE_DATE <=', $this->input->post('akhirGrouping'));
  //     }
        
  //     $this->db->order_by('det.TANGGAL_LAYANAN', 'DESC');

  
  //     $this->db->limit($length, $start);
  //     $query = $this->db->get();
  //     return $query->result();
  //   }
    
  //   public function total_count()
  //   {
  //       $this->db->from('db_rekammedis.tb_coding_detail det');
  //       $this->db->where('det.STATUS !=', 1);
  //       return $this->db->count_all_results();
  //   }
    
  //   public function filter_count($searchValue = null){
  //   $this->db->from('db_rekammedis.tb_coding_detail det');
	// 	$this->db->where('det.STATUS !=', 1);
		
	// 	if ($searchValue) {
  //     $this->db->like('det.NORM', $searchValue);
  // }

  //   if ($this->input->post('norm')) {
  //       $this->db->where('det.NORM', $this->input->post('norm'));
  //   }
  //   if ($this->input->post('awalLayanan')) {
  //       $this->db->where('det.TANGGAL_LAYANAN >=', $this->input->post('awalLayanan'));
  //   }
  //   if ($this->input->post('akhirLayanan')) {
  //       $this->db->where('det.TANGGAL_LAYANAN <=', $this->input->post('akhirLayanan'));
  //   }
  //   if ($this->input->post('awalGrouping')) {
  //     $this->db->where('det.DONE_DATE >=', $this->input->post('awalGrouping'));
  // }
  // if ($this->input->post('akhirGrouping')) {
  //     $this->db->where('det.DONE_DATE <=', $this->input->post('akhirGrouping'));
  // }

	// 	return $this->db->count_all_results();
	// }

  public function getHistory($userid, $start, $length, $searchValue = null)
  {
    $this->db->select("
        dr.DESKRIPSI AS statusSEP,
        DATE_FORMAT(det.DONE_DATE, '%d-%m-%Y') AS DONE_DATE,
        det.ID,
        cod.TANGGAL_TARIK,
        det.TANGGAL_LAYANAN,
        kod.NAMA AS COSTING,
        det.NORM,
        det.NAMA_PASIEN,
        det.NOPEN,
        mr.DESKRIPSI,
        det.TANGGAL_PENDAFTARAN,
        det.SEP,
        det.HASIL_RAD,
        det.STATUS
    ");
    $this->db->from('db_rekammedis.tb_coding_detail det');
    $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
    $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
    $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
    $this->db->join('db_rekammedis.koder kod', 'kod.ID = det.DONE_BY', 'left');
    $this->db->where('cod.ID_PETUGAS', $userid);
    $this->db->where('det.STATUS =', 2);  

    if ($this->input->post('norm')) {
        $this->db->where('det.NORM', $this->input->post('norm'));
    }
    if ($this->input->post('awalLayanan')) {
      $awalLayanan = date('Y-m-d H:i:s', strtotime($this->input->post('awalLayanan') . ' 00:00:00'));
      $this->db->where('det.TANGGAL_LAYANAN >=', $awalLayanan);
    }
    if ($this->input->post('akhirLayanan')) {
        $akhirLayanan = date('Y-m-d H:i:s', strtotime($this->input->post('akhirLayanan') . ' 23:59:59'));
        $this->db->where('det.TANGGAL_LAYANAN <=', $akhirLayanan);
    }
    if ($this->input->post('awalGrouping')) {
        $awalGrouping = date('Y-m-d H:i:s', strtotime($this->input->post('awalGrouping') . ' 00:00:00'));
        $this->db->where('det.DONE_DATE >=', $awalGrouping);
    }
    if ($this->input->post('akhirGrouping')) {
        $akhirGrouping = date('Y-m-d H:i:s', strtotime($this->input->post('akhirGrouping') . ' 23:59:59'));
        $this->db->where('det.DONE_DATE <=', $akhirGrouping);
    }
    $this->db->order_by('det.TANGGAL_LAYANAN', 'DESC');

    if (!empty($searchValue)) {
			$this->db->group_start(); 
			$this->db->like('det.TANGGAL_LAYANAN', $searchValue);
			$this->db->or_like('det.NORM', $searchValue);
			$this->db->or_like('det.NAMA_PASIEN', $searchValue);
			$this->db->or_like('det.SEP', $searchValue);
			$this->db->group_end();
		}
    $recordsFiltered = $this->db->count_all_results('', false); 
    $this->db->limit($length, $start);
    $query = $this->db->get();

    return array(
    'recordsTotal' => $this->getTotalRecord($userid),
    'recordsFiltered' => $recordsFiltered,
    'data' => $query->result()
    );
  }
  public function getTotalRecord($userid)
  {
      $this->db->from('db_rekammedis.tb_coding_detail det');
      $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
      $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
      $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
      $this->db->join('db_rekammedis.koder kod', 'kod.ID = det.DONE_BY', 'left');
      $this->db->where('cod.ID_PETUGAS', $userid);
      $this->db->where('det.STATUS =', 2);  
      
    if ($this->input->post('norm')) {
      $this->db->where('det.NORM', $this->input->post('norm'));
    }
    if ($this->input->post('awalLayanan')) {
      $awalLayanan = date('Y-m-d H:i:s', strtotime($this->input->post('awalLayanan') . ' 00:00:00'));
      $this->db->where('det.TANGGAL_LAYANAN >=', $awalLayanan);
    }
    if ($this->input->post('akhirLayanan')) {
        $akhirLayanan = date('Y-m-d H:i:s', strtotime($this->input->post('akhirLayanan') . ' 23:59:59'));
        $this->db->where('det.TANGGAL_LAYANAN <=', $akhirLayanan);
    }
    if ($this->input->post('awalGrouping')) {
        $awalGrouping = date('Y-m-d H:i:s', strtotime($this->input->post('awalGrouping') . ' 00:00:00'));
        $this->db->where('det.DONE_DATE >=', $awalGrouping);
    }
    if ($this->input->post('akhirGrouping')) {
        $akhirGrouping = date('Y-m-d H:i:s', strtotime($this->input->post('akhirGrouping') . ' 23:59:59'));
        $this->db->where('det.DONE_DATE <=', $akhirGrouping);
    }     

		return $this->db->count_all_results();
  }


// public function total_count()
// {
//     $this->db->from('db_rekammedis.tb_coding_detail det');
//     $this->db->where('det.STATUS !=', 1);
//     return $this->db->count_all_results();
// }

// public function filter_count($searchValue = null)
// {
//     $this->db->from('db_rekammedis.tb_coding_detail det');
//     $this->db->where('det.STATUS !=', 1);

//     if ($searchValue) {
//         $this->db->like('det.NORM', $searchValue);
//     }

//     if ($this->input->post('norm')) {
//         $this->db->where('det.NORM', $this->input->post('norm'));
//     }
//     if ($this->input->post('awalLayanan')) {
//       $awalLayanan = date('Y-m-d H:i:s', strtotime($this->input->post('awalLayanan') . ' 00:00:00'));
//       $this->db->where('det.TANGGAL_LAYANAN >=', $awalLayanan);
//   }
//   if ($this->input->post('akhirLayanan')) {
//       $akhirLayanan = date('Y-m-d H:i:s', strtotime($this->input->post('akhirLayanan') . ' 23:59:59'));
//       $this->db->where('det.TANGGAL_LAYANAN <=', $akhirLayanan);
//   }
//   if ($this->input->post('awalGrouping')) {
//       $awalGrouping = date('Y-m-d H:i:s', strtotime($this->input->post('awalGrouping') . ' 00:00:00'));
//       $this->db->where('det.DONE_DATE >=', $awalGrouping);
//   }
//   if ($this->input->post('akhirGrouping')) {
//       $akhirGrouping = date('Y-m-d H:i:s', strtotime($this->input->post('akhirGrouping') . ' 23:59:59'));
//       $this->db->where('det.DONE_DATE <=', $akhirGrouping);
//   }
//     return $this->db->count_all_results();
// }

public function getSep($nopen) {
  // Menggunakan query builder untuk membuat query yang sama
  $this->db->select('p.NOMOR, pj.NOMOR as SEP, r.DESKRIPSI, p.TANGGAL');
  $this->db->from('pendaftaran.kunjungan pk');
  $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
  $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'left');
  $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'left');
  $this->db->join('master.ruangan r', 'r.ID = pk.RUANGAN', 'left');
  $this->db->join('master.ruangan rtp', 'rtp.ID = tp.RUANGAN', 'left');
  $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'left');
  $this->db->join('pembayaran.tagihan_pendaftaran tap', 'tap.PENDAFTARAN = p.NOMOR', 'left');
  $this->db->join('pembayaran.costing co', 'co.TAGIHAN = tap.TAGIHAN', 'left');
  $this->db->where('p.NOMOR', $nopen);
  $this->db->where('pk.STATUS !=', 0);
  $this->db->where_not_in('r.JENIS_KUNJUNGAN', [0, 2, 3, 4, 11, 14]);
  $this->db->where('pj.JENIS', 2);
  $this->db->where('co.ID IS NOT NULL', null, false); 
  $this->db->group_by('p.NOMOR');
  $this->db->order_by('pk.MASUK', 'DESC');
  $query = $this->db->get();
  return $query->result();
}
function list_Petugas(){
  if ($this->input->get('q')) {
      $this->db->like('op.nama_lengkap ', $this->input->get('q'));
  }
  $this->db->select("*");
  $this->db->from('db_rekammedis.tb_operator op');
  $this->db->where("op.koding",1);
  $this->db->where("op.TUGAS !=",9);
  $this->db->order_by('op.id', 'ASC');
  
  $query = $this->db->get();
  return $query->result_array();
}
public function dataListcosting_pindah($userid, $start, $length, $search_value)
{
    // Query utama dengan filter dan data
    $this->db->select("
        dr.DESKRIPSI AS statusSEP,
        DATE_FORMAT(det.DONE_DATE, '%d-%m-%Y') AS DONE_DATE,
        det.ID,
        cod.TANGGAL_TARIK,
        det.TANGGAL_LAYANAN,
        kod.NAMA AS COSTING,
        det.NORM,
        det.NAMA_PASIEN,
        det.NOPEN,
        mr.DESKRIPSI,
        det.TANGGAL_PENDAFTARAN,
        det.SEP,
        det.HASIL_RAD,
        det.STATUS_COSTING,
        det.ID_PETUGAS,
        top2.nama_lengkap AS namaPetugas,
        det.STATUS
    ");
    $this->db->from('db_rekammedis.coding_detail_log dcd');
    $this->db->join('db_rekammedis.tb_coding_detail det', 'det.ID = dcd.ID_CODING_DET', 'left');
    $this->db->join('db_rekammedis.referensi dr', 'dr.ID = det.STATSEP', 'left');
    $this->db->join('db_rekammedis.tb_operator top2', 'top2.ID = dcd.OLEH', 'left');
    $this->db->join('db_rekammedis.tb_coding cod', 'cod.ID = det.ID_CODING', 'left');
    $this->db->join('master.ruangan mr', 'mr.ID = cod.ID_RUANGAN', 'left');
    $this->db->join('db_rekammedis.koder kod', 'kod.ID = det.DONE_BY', 'left');
    $this->db->where('dcd.ID_PETUGAS_NEW', $userid);
    $this->db->where('det.STATUS', 3);
    $this->db->where('dcd.STATUS', 1);

    // Clone query sebelum limit untuk menghitung total dan filtered
    $filtered_query = clone $this->db;

    // Tambahkan filter pencarian
    if (!empty($search_value)) {
        $this->db->group_start();
        $this->db->like("det.NAMA_PASIEN", $search_value);
        $this->db->or_like("det.NORM", $search_value);
        $this->db->or_like("det.SEP", $search_value);
        $this->db->group_end();
    }

    // Pagination
    $this->db->order_by('det.TANGGAL_LAYANAN', 'ASC');
    $this->db->limit($length, $start);
    $query = $this->db->get();

    // Hitung filtered total
    $filtered_query->select("COUNT(*) AS total");
    $filtered_result = $filtered_query->get()->row()->total;

    // Hitung total tanpa filter
    $this->db->reset_query();
    $this->db->from('db_rekammedis.coding_detail_log dcd');
    $this->db->join('db_rekammedis.tb_coding_detail det', 'det.ID = dcd.ID_CODING_DET', 'left');
    $this->db->where('dcd.ID_PETUGAS_NEW', $userid);
    $this->db->where('det.STATUS', 3);
    $this->db->where('dcd.STATUS', 1);
    $recordsTotal = $this->db->count_all_results();

    return [
        'data' => $query,
        'recordsTotal' => $recordsTotal,
        'recordsFiltered' => $filtered_result,
    ];
}
public function getJumlahTugas($userid) {
  $this->db->select('COUNT(*) AS total');
  $this->db->from('db_rekammedis.coding_detail_log');
  $this->db->where('STATUS', 1);
  $this->db->where('ID_PETUGAS_NEW',$userid);
  $query = $this->db->get();

  return $query->result();
}

function listidrgicd9cm($params)
{
    $this->db->select("p.NOPEN, p.TINDAKAN, px.CODE ICD9CM, px.DESCRIPTION DESK_ICD9CM, p.ID ID_TINDAKAN
    , IF(p.UTAMA = 1, 'Primary', 'Secondary') AS KATEGORI, p.JUMLAH");
    $this->db->from('medicalrecord.prosedur_ p');
    $this->db->join('medicalrecord.code_idrg px', 'px.CODE = p.KODE', 'left');
    $this->db->where("p.STATUS = 1");
    $this->db->where("px.SYSTEM LIKE '%ICD_9CM_2010_IM%'");
    $this->db->where("p.NOPEN = '$params'");
}


    function datatableListidrgicd9cm($params){
        $this->listidrgicd9cm($params);
        if($_POST["length"] != -1){
        $this->db->limit($_POST["length"], $_POST["start"]);
        }
        $query = $this->db->get();
        return $query->result();
    }

    function filter_count_listidrgicd9cm($params){
        $this->listidrgicd9cm($params);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function total_count_listidrgicd9cm($params){
        $this->listidrgicd9cm($params);
        return $this->db->count_all_results();
    }

function listidrgicd10($params)
{
    $this->db->select("d.NOPEN, d.DIAGNOSA, dx.CODE ICD10, dx.DESCRIPTION DESK_ICD10, d.ID ID_DIAGNOSA
    , IF(d.UTAMA = 1, 'Primary', 'Secondary') AS KATEGORI");
    $this->db->from('medicalrecord.diagnosa_ d');
    $this->db->join('medicalrecord.code_idrg dx', 'dx.CODE = d.KODE', 'left');
    $this->db->where("d.STATUS = 1");
    $this->db->where("dx.SYSTEM LIKE '%ICD_10_2010_IM%'");
    $this->db->where("d.NOPEN = '$params'");
}


    function datatableListidrgicd10($params){
        $this->listidrgicd10($params);
        if($_POST["length"] != -1){
        $this->db->limit($_POST["length"], $_POST["start"]);
        }
        $query = $this->db->get();
        return $query->result();
    }

    function filter_count_listidrgicd10($params){
        $this->listidrgicd10($params);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function total_count_listidrgicd10($params){
        $this->listidrgicd10($params);
        return $this->db->count_all_results();
    }

public function listTanggal() {
  $this->db->distinct();
  $this->db->select("DATE(TANGGAL_LAYANAN) AS ID, DATE_FORMAT(TANGGAL_LAYANAN, '%d/%m/%Y') AS text", false);
  $this->db->from('db_rekammedis.tb_coding_detail');
  $this->db->group_by("DATE(TANGGAL_LAYANAN)");
  $this->db->order_by('TANGGAL_LAYANAN', 'DESC');
  
  $query = $this->db->get();

  return $query->result_array();
}
public function jumlahData($userid) {
  $this->db->select('COUNT(*) as total');
    $this->db->from('db_rekammedis.tb_coding_detail');
    $this->db->where('STATUS', 2);
    $this->db->where('DONE_BY', $userid);
    $this->db->where('MONTH(TANGGAL_LAYANAN)', date('m'));
    $this->db->where('YEAR(TANGGAL_LAYANAN)', date('Y'));
    $query = $this->db->get();
    $result = $query->row();
    return $result->total;
}

}

?>
<?php
/*
This file is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This file is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with This file.  If not, see <http://www.gnu.org/licenses/>.

tsuyu

*/

require_once("http://localhost:8080/JavaBridge/java/Java.inc");

try {
    $namaReport = [
        'pasienpdf' => 'Pasienrj',
        'pasienxls' => 'Pasienrj',
        'reservasipdf' => 'reservasitunggu',
        'reservasixls' => 'reservasitunggu',
        'kemopdf' => 'laporanStagnasiKemo',
        'kemoxls' => 'laporanStagnasiKemo',
        'stagnasipdf' => 'laporanStagnasi',
        'stagnasixls' => 'laporanStagnasi',
        'penjadwalanpdf' => 'penjadwalan_operasi',
        'penjadwalanxls' => 'penjadwalan_operasi',
        'perjanjianpdf' => 'penjadwalan_operasi',
        'perjanjianxls' => 'penjadwalan_operasi',

    ];

    $format = strpos($_GET['report'], 'xls') !== false ? 'xls' : 'pdf';
    $formatKey = $_GET['report'] ?? '';
    $reportFile = $namaReport[$formatKey] ?? null;

    if (!$reportFile) {
        throw new Exception("Jenis laporan tidak valid.");
    }

    $jasperxml = new java("net.sf.jasperreports.engine.xml.JRXmlLoader");
    $jasperDesign = $jasperxml->load(realpath("$reportFile.jrxml"));

    $compileManager = new JavaClass("net.sf.jasperreports.engine.JasperCompileManager");
    $report = $compileManager->compileReport($jasperDesign);

    $params = new Java("java.util.HashMap");
    if ($formatKey === 'kemopdf' || $formatKey === 'kemoxls') {
        $params->put("TGL_AKHIR", date("Y-m-d 23:59:59", strtotime($_GET["TANGGAL_AKHIR_REKAP"])));
    } elseif (in_array($formatKey, ['penjadwalanpdf', 'penjadwalanxls', 'perjanjianpdf', 'perjanjianxls'])) {
        // Parameter khusus untuk laporan penjadwalan operasi
        $params->put("TGL_AWAL", date("Y-m-d 00:00:01", strtotime($_GET["TANGGAL_AWAL_REKAP"])));
        $params->put("TGL_AKHIR", date("Y-m-d 23:59:59", strtotime($_GET["TANGGAL_AKHIR_REKAP"])));

        // Parameter tambahan untuk membedakan jenis laporan
        if (strpos($formatKey, 'penjadwalan') !== false) {
            $params->put("JENIS_LAPORAN", "PENJADWALAN");
        } else {
            $params->put("JENIS_LAPORAN", "PERJANJIAN");
        }
    } else {
        $params->put("TGL_AWAL", date("Y-m-d 00:00:01", strtotime($_GET["TANGGAL_AWAL_REKAP"])));
        $params->put("TGL_AKHIR", date("Y-m-d 23:59:59", strtotime($_GET["TANGGAL_AKHIR_REKAP"])));
    }
    // $params->put("TGL_AWAL", date("Y-m-d 00:00:01", strtotime($_GET["TANGGAL_AWAL_REKAP"])));
    // $params->put("TGL_AKHIR", date("Y-m-d 23:59:59", strtotime($_GET["TANGGAL_AKHIR_REKAP"])));

    $class = new JavaClass("java.lang.Class");
    $class->forName("com.mysql.jdbc.Driver");
    $driverManager = new JavaClass("java.sql.DriverManager");
    $conn = $driverManager->getConnection("*******************************************", "simrsdev", "G0l0ks4kt1");

    $fillManager = new JavaClass("net.sf.jasperreports.engine.JasperFillManager");
    $jasperPrint = $fillManager->fillReport($report, $params, $conn);

    $tglAwalFormatted = date("dmy", strtotime($_GET["TANGGAL_AWAL_REKAP"]));
    $tglAkhirFormatted = date("dmy", strtotime($_GET["TANGGAL_AKHIR_REKAP"]));
    $outputName = "Laporan_" . ucfirst(strtolower($reportFile)) . "_[{$tglAwalFormatted}_{$tglAkhirFormatted}]";


    $exporter = $format === 'xls' 
        ? new java("net.sf.jasperreports.engine.export.JRXlsExporter") 
        : new java("net.sf.jasperreports.engine.export.JRPdfExporter");

    $outputPath = realpath(".") . "\\{$outputName}.{$format}";
    $exporter->setParameter(java("net.sf.jasperreports.engine.JRExporterParameter")->JASPER_PRINT, $jasperPrint);
    $exporter->setParameter(java("net.sf.jasperreports.engine.JRExporterParameter")->OUTPUT_FILE_NAME, $outputPath);

    if ($format === 'xls') {
        $exporter->setParameter(java("net.sf.jasperreports.engine.export.JRXlsExporterParameter")->IS_ONE_PAGE_PER_SHEET, java("java.lang.Boolean")->FALSE);
        $exporter->setParameter(java("net.sf.jasperreports.engine.export.JRXlsExporterParameter")->IS_WHITE_PAGE_BACKGROUND, java("java.lang.Boolean")->FALSE);
        $exporter->setParameter(java("net.sf.jasperreports.engine.export.JRXlsExporterParameter")->IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, java("java.lang.Boolean")->TRUE);
        header("Content-type: application/vnd.ms-excel");
        header("Content-Disposition: attachment; filename={$outputName}.xls");
    } else {
        header("Content-type: application/pdf");
        header("Content-Disposition: attachment; filename={$outputName}.pdf");
    }

    $exporter->exportReport();
    readfile($outputPath);
    unlink($outputPath);

} catch (Exception $ex) {
    echo "Error: " . $ex->getMessage();
}
?>


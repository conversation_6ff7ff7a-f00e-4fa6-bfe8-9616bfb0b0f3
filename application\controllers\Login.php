<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {

public function __construct()
{
    parent::__construct();
    //Do your magic here
    $this->load->model('ModelLogin');
}

	public function index()
	{
		$data = array(
			'title' 		=> 'Login' ,
		);
		$this->load->view('Login', $data, FALSE);		
	}
   

  public function signin() {
    $username = $this->input->post('username');
    $password = $this->input->post('password');

    $host = $_SERVER['HTTP_HOST'];
    $host_diizinkan = array('************', '127.0.0.1', 'localhost', '*************', '*************','*************','************');

    if (in_array($host, $host_diizinkan)) { 
        // Login lokal
        // Cek login operator biasa
        $data_operator = $this->ModelLogin->login($username, $password);
        
        // Cek login dokter (paralel, bukan fallback)
        $data_dokter = $this->ModelLogin->loginPenggunaDokter($username, $password);

        // Determine login type using switch-case
        switch (true) {
            case (strcasecmp($username, 'radiologi') === 0):
                // Login radiologi
                $data = $this->ModelLogin->login_radiologi($username, $password);
                $this->proses_sign_in($data, $password);
                break;
                
            case ($data_dokter):
                // Login sebagai dokter (prioritas pertama)
                // REAL: Login dokter dengan verifikasi password
                $this->proses_sign_in_dokter($data_dokter, $password);

                // UJICOBA: Bypass password mode
                // $this->proses_sign_in_dokter_bypass($data_dokter, $password);
                break;
                
            case ($data_operator):
                // Login sebagai operator
                $this->proses_sign_in($data_operator, $password);
                break;
                
            default:
                // Jika semua gagal
                $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
                echo json_encode($result);
                break;
        }
    } else {
        // Login eksternal
        // Cek login operator biasa
        $data_operator = $this->ModelLogin->login($username, $password);
        
        // Cek login dokter (paralel, bukan fallback)
        $data_dokter = $this->ModelLogin->loginPenggunaDokter($username, $password);

        // Determine login type using switch-case
        switch (true) {
            case (strcasecmp($username, 'radiologi') === 0):
                // Login radiologi
                $data = $this->ModelLogin->login_radiologi($username, $password);
                $this->proses_sign_in($data, $password);
                break;
                
            case ($data_dokter):
                // Login sebagai dokter (prioritas pertama)
                // REAL: Login dokter dengan verifikasi password
                $this->proses_sign_in_dokter($data_dokter, $password);

                // UJICOBA: Bypass password mode
                // $this->proses_sign_in_dokter_bypass($data_dokter, $password);
                break;
                
            case ($data_operator):
                // Login sebagai operator
                $this->proses_sign_in($data_operator, $password);
                break;
                
            default:
                // Jika semua gagal
                $result = array('status' => 204, 'message' => 'Akses ditolak');
                echo json_encode($result);
                break;
        }
    }
  }

//   login tanpa password
  private function proses_sign_in($data, $password) {
    if (empty($data)) {
        $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
    } else {
        $private_key = 'KDFLDMSTHBWWSGCBH';
        $hashed_password = $data->PASSWORD;
        $id = $data->id;
        $id_simpel = $data->IDSIMPEL;
        $username = $data->LOGIN;
        $nama = $data->nama_lengkap;
        $id_coder = $data->ID_CODER;
        $tugas = $data->TUGAS;
        $status_operator = $data->status;


        switch ($tugas) {
            case 2:
                $link = 'pemi';
                break;
            case 1:
                $link = 'assrj';
                break;
            case 5:
                $link = 'coder_radiologi';
                break;
            case 3:
                $link = 'coder';
                if ($data->status_ds = 1) {
                    $link = 'MedicalRecords';
                }
                break;
            case 4:
                $link = 'kuantitatif';
                break;
            case 9:
                $link = 'admin';
                break;
            case 11:
                $link = 'admision';
                break;
            default:
                $link = 'perjanjianH';
                break;
        }

        

        // $passwordMD5 = md5($private_key . md5($password) . $private_key);
        // if (hash_equals($hashed_password, $passwordMD5) || $data->PASSWORD == $data->PASS) {
        
        // Untuk sementara, bypass pengecekan password
        if (true) { 
            if (is_null($nama) || $status_operator != 1) {
                $result = array('status' => 204, 'message' => 'Akses belum diberikan hubungi SIMRS');
            } else {
                if ($link == null) {
                    $result = array('status' => 204, 'message' => 'Akses belum diberikan hubungi SIMRS');
                } else {
                    $session = array(
                        'id' => $id,
                        'username' => $username,
                        'nama' => $nama,
                        'id_coder' => $id_coder,
                        'tugas' => $tugas,
                        'id_simpel' => $id_simpel,
                        'filling' => isset($data->filling) ? $data->filling : 0,
                        'scanner' => isset($data->scanner) ? $data->scanner : 0,
                        'assembling' => isset($data->assembling) ? $data->assembling : 0,
                        'laporan' => isset($data->laporan) ? $data->laporan : 0,
                        'koding' => isset($data->koding) ? $data->koding : 0,
                        'analisis' => isset($data->analisis) ? $data->analisis : 0,
                        'radiologi' => isset($data->radiologi) ? $data->radiologi : 0,
                        'perjanjian' => isset($data->perjanjian) ? $data->perjanjian : 0,
                        'admision' => isset($data->admision) ? $data->admision : 0,
                        'stat_admision' => isset($data->stat_admision)? $data->stat_admision : 0,
                        'status_ds' => isset($data->status_ds) ? $data->status_ds : 0,
                        'link' => $link,
                        'logged_in' => TRUE,
                    );
                    $this->session->set_userdata($session);
                    $result = array('status' => 200, 'message' => 'Selamat Datang <br>' . $nama, 'data' => $session);
                }
            }
        } else {
            $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
        }
    }
    echo json_encode($result);
  }

// login

// private function proses_sign_in($data, $password) {
//     if (empty($data)) {
//         $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
//     } else {
//         $private_key = 'KDFLDMSTHBWWSGCBH';
//         $hashed_password = $data->PASSWORD;
//         $id = $data->id;
//         $id_simpel = $data->IDSIMPEL;
//         $username = $data->LOGIN;
//         $nama = $data->nama_lengkap;
//         $id_coder = $data->ID_CODER;
//         $tugas = $data->TUGAS;
//         $status_operator = $data->status;
    


//         // arah ke-menu
//         switch ($tugas) {
//             case 2:
//                 $link = 'pemi';
//                 break;
//             case 1:
//                 $link = 'assrj';
//                 break;
//             case 5:
//                 $link = 'coder_radiologi';
//                 break;
//             case 3:
//                 $link = 'coder';
//                    if ($data->status_ds = 1) {
//                         $link = 'MedicalRecords';
//                     }
//                 break;
//             case 4:
//                 $link = 'kuantitatif';
//                 break;
//             case 9:
//                 $link = 'admin';
//                 break;
//             case 11:
//                 $link = 'admision';
//                 break;
//             default:
//                 $link = 'perjanjianH';
//                 break;
//         }
       


//         // Verifikasi password
//         if (strcasecmp($username, 'radiologi') !== 0) {
//             $passwordMD5 = md5($private_key . md5($password) . $private_key);
//             if (hash_equals($hashed_password, $passwordMD5) || $data->PASSWORD == $data->PASS) {
//                 if (is_null($nama) || $status_operator != 1) {
//                     $result = array('status' => 204, 'message' => 'Akses belum diberikan, hubungi SIMRS');
//                 } else {
//                     if ($link == null) {
//                         $result = array('status' => 204, 'message' => 'Akses belum diberikan, hubungi SIMRS');
//                     } else {
//                         $session = array(
//                             'id' => $id,
//                             'username' => $username,
//                             'nama' => $nama,
//                             'id_coder' => $id_coder,
//                             'id_simpel' => $id_simpel,
//                             'tugas' => $tugas,
//                             'link' => $link,
//                             'filling' => isset($data->filling) ? $data->filling : 0,
//                             'scanner' => isset($data->scanner) ? $data->scanner : 0,
//                             'assembling' => isset($data->assembling) ? $data->assembling : 0,
//                             'laporan' => isset($data->laporan) ? $data->laporan : 0,
//                             'koding' => isset($data->koding) ? $data->koding : 0,
//                             'analisis' => isset($data->analisis) ? $data->analisis : 0,
//                             'radiologi' => isset($data->radiologi) ? $data->radiologi : 0,
//                              'perjanjian' => isset($data->perjanjian) ? $data->perjanjian : 0,
//                              'admision' => isset($data->admision) ? $data->admision : 0,
//                              'stat_admision' => isset($data->stat_admision)? $data->stat_admision : 0,
//                              'status_ds' => isset($data->status_ds) ? $data->status_ds : 0,
//                              'logged_in' => TRUE,
//                         );
//                         $this->session->set_userdata($session);
//                         $result = array('status' => 200, 'message' => 'Selamat Datang <br>' . $nama, 'data' => $session);
//                     }
//                 }
//             } else {
//                 $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
//             }
//         } else {
//             // Jika pengguna adalah radiologi, hanya periksa nama dan status
//             if (is_null($nama) || $status_operator != 1) {
//                 $result = array('status' => 204, 'message' => 'Akses belum diberikan, hubungi SIMRS');
//             } else {
//                 if ($link == null) {
//                     $result = array('status' => 204, 'message' => 'Akses belum diberikan, hubungi SIMRS');
//                 } else {
//                     $session = array(
//                         'id' => $id,
//                         'username' => $username,
//                         'nama' => $nama,
//                         'id_coder' => $id_coder,
//                         'tugas' => $tugas,
//                         'link' => $link,
//                         'radiologi' => isset($data->radiologi) ? $data->radiologi : 0,
//                         'logged_in' => TRUE,
//                     );
//                     $this->session->set_userdata($session);
//                     $result = array('status' => 200, 'message' => 'Selamat Datang <br>' . $nama, 'data' => $session);
//                 }
//             }
//         }
//     }
//     echo json_encode($result);
// }



  // ===============================================
  // PILIHAN FUNGSI LOGIN DOKTER - PILIH SALAH SATU
  // ===============================================

  // FUNGSI 1: Login Dokter BYPASS Password 
  private function proses_sign_in_dokter_bypass($data, $password) {
    // DEBUG LOG: hasil data dokter bypass
    error_log("BYPASS_DOKTER: " . print_r($data, true));
    if (empty($data)) {
        $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
    } else {
        $id_simpel = $data->IDSIMPEL;
        $username = $data->LOGIN;
        $nip = $data->NIP;
        $nama_dokter = $data->nama_dokter;

        // VALIDASI: Pastikan data dokter lengkap
        if (empty($nama_dokter) || empty($nip)) {
            $result = array('status' => 204, 'message' => 'Data dokter tidak lengkap di database');
        } else {
            // BYPASS: Login tanpa verifikasi password (untuk ujicoba)
            // TIDAK ADA PENGECEKAN tb_operator atau status - langsung masuk jika data dokter ada
            $session = array(
                'id' => $id_simpel,
                'username' => $username,
                'nama' => $nama_dokter,
                'id_coder' => null,
                'tugas' => 99, // Tugas khusus untuk dokter 
                'id_simpel' => $id_simpel,
                'nip' => $nip,
                'nama_dokter' => $nama_dokter,
                'filling' => 0,
                'scanner' => 0,
                'assembling' => 0,
                'laporan' => 0,
                'koding' => 0,
                'analisis' => 0,
                'radiologi' => 0,
                'perjanjian' => 0,
                'admision' => 0,
                'stat_admision' => 0,
                'status_ds' => 2, // Status dokter
                'link' => 'MedicalRecords',
                'logged_in' => TRUE,
            );
            $this->session->set_userdata($session);
            $result = array('status' => 200, 'message' => 'Selamat Datang Dr. ' . $nama_dokter . ' (BYPASS MODE)', 'data' => $session);
        }
    }
    echo json_encode($result);
  }

  // FUNGSI 2: Login Dokter DENGAN Verifikasi Password (mengikuti proses_sign_in utama)
  private function proses_sign_in_dokter($data, $password) {
    if (empty($data)) {
        $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
    } else {
        $private_key = 'KDFLDMSTHBWWSGCBH';
        $hashed_password = $data->PASSWORD;
        $id_simpel = $data->IDSIMPEL;
        $username = $data->LOGIN;
        $nip = $data->NIP;
        $nama_dokter = $data->nama_dokter;

        // Enkripsi password sama seperti proses_sign_in utama
        $passwordMD5 = md5($private_key . md5($password) . $private_key);

        if (hash_equals($hashed_password, $passwordMD5)) {
            // Set session dengan status_ds = 2 (akses dokter)
            $session = array(
                'id' => $id_simpel,
                'username' => $username,
                'nama' => $nama_dokter,
                'id_coder' => null,
                'tugas' => 99, // Tugas khusus untuk dokter 
                'id_simpel' => $id_simpel,
                'nip' => $nip,
                'nama_dokter' => $nama_dokter,
                'filling' => 0,
                'scanner' => 0,
                'assembling' => 0,
                'laporan' => 0,
                'koding' => 0,
                'analisis' => 0,
                'radiologi' => 0,
                'perjanjian' => 0,
                'admision' => 0,
                'stat_admision' => 0,
                'status_ds' => 2, // Status dokter
                'link' => 'MedicalRecords',
                'logged_in' => TRUE,
            );
            $this->session->set_userdata($session);
            $result = array('status' => 200, 'message' => 'Selamat Datang Dr. ' . $nama_dokter, 'data' => $session);
        } else {
            $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
        }
    }
    echo json_encode($result);
  }

}

/* End of file Login.php */
/* Location: ./application/controllers/Login.php */
